<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: login.php");
    exit();
}

include "db.php";

echo "<h2>Debug Family Detail Page</h2>";

// Show all GET parameters
echo "<h3>GET Parameters:</h3>";
echo "<pre>";
print_r($_GET);
echo "</pre>";

// Get family ID from URL
if (!isset($_GET['id'])) {
    echo "<p style='color: red;'>ERROR: No ID parameter provided in URL</p>";
    echo "<p>Expected URL format: debug_family_detail.php?id=123</p>";
    exit();
}

$family_id = $_GET['id'];
echo "<p>Raw ID from URL: " . htmlspecialchars($family_id) . "</p>";

$family_id = (int)$_GET['id'];
echo "<p>Converted ID to integer: " . $family_id . "</p>";

// Validate that we have a valid ID
if ($family_id <= 0) {
    echo "<p style='color: red;'>ERROR: Invalid ID (must be positive integer)</p>";
    exit();
}

// Test database connection
echo "<h3>Database Connection:</h3>";
if ($conn->connect_error) {
    echo "<p style='color: red;'>ERROR: Database connection failed: " . $conn->connect_error . "</p>";
    exit();
} else {
    echo "<p style='color: green;'>✓ Database connection successful</p>";
}

// Query to get family record
$sql = "SELECT * FROM family_registration WHERE id = ?";
echo "<h3>SQL Query:</h3>";
echo "<p>Query: " . htmlspecialchars($sql) . "</p>";
echo "<p>Parameter: " . $family_id . "</p>";

$stmt = $conn->prepare($sql);

if (!$stmt) {
    echo "<p style='color: red;'>ERROR: Database prepare failed: " . $conn->error . "</p>";
    exit();
} else {
    echo "<p style='color: green;'>✓ Statement prepared successfully</p>";
}

$stmt->bind_param("i", $family_id);
$stmt->execute();
$result = $stmt->get_result();

echo "<h3>Query Results:</h3>";
echo "<p>Number of rows found: " . $result->num_rows . "</p>";

if ($result->num_rows === 0) {
    echo "<p style='color: red;'>ERROR: No record found for ID " . $family_id . "</p>";
    
    // Let's check what IDs actually exist
    echo "<h3>Available IDs in database:</h3>";
    $check_sql = "SELECT id, family_number FROM family_registration ORDER BY id DESC LIMIT 10";
    $check_result = $conn->query($check_sql);
    
    if ($check_result && $check_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Family Number</th></tr>";
        while ($row = $check_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['family_number']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No records found in database at all!</p>";
    }
    
    exit();
} else {
    echo "<p style='color: green;'>✓ Record found successfully</p>";
}

$record = $result->fetch_assoc();

echo "<h3>Record Data:</h3>";
echo "<pre>";
print_r($record);
echo "</pre>";

echo "<h3>Test Links:</h3>";
echo "<p><a href='view_family_detail.php?id=" . $family_id . "'>Go to actual detail page</a></p>";
echo "<p><a href='view_family_records.php'>Back to records</a></p>";

$conn->close();
?>

<!DOCTYPE html>
<html>
<head>
    <title>Debug Family Detail</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h2, h3 { color: #333; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; }
        table { margin: 10px 0; }
        th, td { padding: 8px; text-align: left; }
        th { background: #f0f0f0; }
    </style>
</head>
<body>
    <!-- Content is generated above -->
</body>
</html>
