<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: login.php");
    exit();
}

// Database connection
$conn = new mysqli("localhost", "root", "", "lhhc");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Number of records per page
$records_per_page = 5;

// Get current page
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $records_per_page;

// Get unique registered_by users who have actually registered families
$registered_by_sql = "SELECT DISTINCT registered_by FROM family_registration WHERE registered_by IS NOT NULL AND registered_by != '' ORDER BY registered_by ASC";
$registered_by_result = $conn->query($registered_by_sql);
$registered_by_users = [];
while ($row = $registered_by_result->fetch_assoc()) {
    $registered_by_users[] = $row['registered_by'];
}

// Handle search and filter
$search = isset($_GET['search']) ? $_GET['search'] : '';
$registered_by_filter = isset($_GET['registered_by']) ? $_GET['registered_by'] : '';
$duplicates_filter = isset($_GET['duplicates']) ? (int)$_GET['duplicates'] : 0;
$where_clause = '';

if (!empty($search) || !empty($registered_by_filter) || $duplicates_filter) {
    $conditions = [];

    if (!empty($search)) {
        $search = $conn->real_escape_string($search);
        $conditions[] = "(registration_date LIKE '%$search%' OR
            family_number LIKE '%$search%' OR
            male_name LIKE '%$search%' OR
            female_name LIKE '%$search%' OR
            address LIKE '%$search%' OR
            remarks LIKE '%$search%')";
    }

    if (!empty($registered_by_filter)) {
        $registered_by_filter = $conn->real_escape_string($registered_by_filter);
        $conditions[] = "LOWER(registered_by) = LOWER('$registered_by_filter')";
    }

    if ($duplicates_filter) {
        $conditions[] = "(family_number IN (
            SELECT family_number
            FROM family_registration
            GROUP BY family_number
            HAVING COUNT(*) > 1
        ) OR
        (TRIM(male_name) != '' AND male_name IN (
            SELECT male_name
            FROM family_registration
            WHERE TRIM(male_name) != ''
            GROUP BY male_name
            HAVING COUNT(*) > 1
        )) OR
        (TRIM(female_name) != '' AND female_name IN (
            SELECT female_name
            FROM family_registration
            WHERE TRIM(female_name) != ''
            GROUP BY female_name
            HAVING COUNT(*) > 1
        )))";
    }

    $where_clause = "WHERE " . implode(" AND ", $conditions);
}

// Get total number of records for pagination
$total_records_sql = "SELECT COUNT(*) as count FROM family_registration $where_clause";
$total_records_result = $conn->query($total_records_sql);
$total_records = $total_records_result->fetch_assoc()['count'];
$total_pages = ceil($total_records / $records_per_page);

// Get duplicate names before pagination
$duplicate_male_names_sql = "
    SELECT male_name FROM family_registration
    WHERE TRIM(male_name) != ''
    GROUP BY male_name
    HAVING COUNT(*) > 1
";
$duplicate_female_names_sql = "
    SELECT female_name FROM family_registration
    WHERE TRIM(female_name) != ''
    GROUP BY female_name
    HAVING COUNT(*) > 1
";
$duplicate_family_numbers_sql = "
    SELECT family_number FROM family_registration
    GROUP BY family_number
    HAVING COUNT(*) > 1
";

$duplicate_male_names_result = $conn->query($duplicate_male_names_sql);
$duplicate_female_names_result = $conn->query($duplicate_female_names_sql);
$duplicate_family_numbers_result = $conn->query($duplicate_family_numbers_sql);

$duplicate_male_names = [];
$duplicate_female_names = [];
$duplicate_family_numbers = [];

while ($row = $duplicate_male_names_result->fetch_assoc()) {
    $duplicate_male_names[] = $row['male_name'];
}
while ($row = $duplicate_female_names_result->fetch_assoc()) {
    $duplicate_female_names[] = $row['female_name'];
}
while ($row = $duplicate_family_numbers_result->fetch_assoc()) {
    $duplicate_family_numbers[] = $row['family_number'];
}

// Set pagination or show all records for duplicates
if ($duplicates_filter) {
    // Show all duplicate records without pagination
    $sql = "SELECT * FROM family_registration $where_clause
            ORDER BY
            SUBSTRING_INDEX(family_number, '-', 1) DESC,
            CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(family_number, '-', 2), '-', -1) AS UNSIGNED) DESC,
            CAST(SUBSTRING_INDEX(family_number, '-', -1) AS UNSIGNED) DESC";
} else {
    // Use pagination for regular view
    $sql = "SELECT * FROM family_registration $where_clause
            ORDER BY
            SUBSTRING_INDEX(family_number, '-', 1) DESC,
            CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(family_number, '-', 2), '-', -1) AS UNSIGNED) DESC,
            CAST(SUBSTRING_INDEX(family_number, '-', -1) AS UNSIGNED) DESC
            LIMIT $offset, $records_per_page";
}
$result = $conn->query($sql);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Family Records - Family Number Management System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Lexend', sans-serif;
        }

        body {
            min-height: 100vh;
            background: #f0f7ff;
        }

        .dashboard {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 250px;
            background: #4db3a8;
            color: white;
            padding: 2rem 0;
        }

        .sidebar-header {
            padding: 0 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .sidebar-header img {
            width: 180px;
            height: 180px;
            margin-bottom: 1.5rem;
            border-radius: 50%;
            object-fit: contain;
            padding: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #000;
            line-height: 1.3;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 2rem;
            color: #000;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            background: #3a8f87;
            color: #000;
        }

        .nav-link i {
            margin-right: 1rem;
            width: 20px;
            color: #000;
        }

        /* Dropdown styles */
        .dropdown-container {
            display: none;
            padding-left: 1rem;
        }

        .dropdown-link {
            display: flex;
            align-items: center;
            padding: 0.8rem 2rem;
            color: #000;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .dropdown-link:hover {
            background: #3a8f87;
            color: #000;
        }

        .dropdown-link i {
            margin-right: 1rem;
            width: 20px;
            color: #000;
        }

        .nav-link.dropdown-btn {
            cursor: pointer;
        }

        .dropdown-btn::after {
            content: '\f107';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            margin-left: auto;
            transition: transform 0.3s;
        }

        .dropdown-btn.active::after {
            transform: rotate(180deg);
        }

        .dropdown-container.show {
            display: block;
        }

        /* Animation for dropdown */
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dropdown-container.show {
            animation: slideDown 0.3s ease-out forwards;
        }

        .main-content {
            flex: 1;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            animation: scaleIn 0.5s ease-out forwards;
            position: relative;
            overflow: hidden;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            animation: fadeIn 0.6s ease-out forwards;
        }

        .header h2 {
            color: #000;
            font-size: 2.2rem;
            margin-bottom: 0.5rem;
        }

        .search-container {
            margin-bottom: 2rem;
            animation: fadeIn 0.7s ease-out forwards;
        }

        .search-form {
            width: 100%;
            max-width: 1000px;
            margin: 0 auto;
        }

        .search-wrapper {
            display: flex;
            gap: 1rem;
            align-items: center;
            justify-content: center;
        }

        .search-input {
            flex: 1;
            padding: 0.8rem 1rem;
            border: 2px solid #ecf0f1;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            color: #000;
        }

        .search-input:focus {
            outline: none;
            border-color: #4db3a8;
            box-shadow: 0 0 0 3px rgba(77, 179, 168, 0.1);
        }

        .search-btn {
            padding: 0.8rem 1.5rem;
            background: linear-gradient(90deg, #4db3a8, #3a8f87);
            color: #000;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(77, 179, 168, 0.3);
        }

        .search-btn::after {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 2s infinite;
        }

        .clear-search {
            padding: 0.8rem 1.5rem;
            background: #e74c3c;
            color: #000;
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }

        .clear-search:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }

        .clear-search::after {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 2s infinite;
        }

        .table-container {
            margin-bottom: 2rem;
            width: 100%;
            overflow-x: visible;
            opacity: 0;
            animation: fadeIn 0.8s ease-out 0.3s forwards;
        }

        /* Adjust for duplicate records view to show more data */
        .duplicates-view .table-container {
            max-height: none;
            overflow-y: visible;
        }

        /* Remove scroll handling from duplicates view */
        body.duplicates-view {
            height: auto;
            overflow: visible;
        }

        /* Ensure table header stays normal */
        .duplicates-view thead {
            position: static;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
            font-size: 14px;
            table-layout: auto;
        }

        th, td {
            padding: 0.75rem 0.5rem;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
            color: #000;
            white-space: normal;
            word-wrap: break-word;
            vertical-align: middle;
            line-height: 1.3;
        }

        /* Adjusted Column widths to fit content better */
        th:nth-child(1), td:nth-child(1) { width: auto; } /* Registration Date */
        th:nth-child(2), td:nth-child(2) { width: auto; } /* Family Number */
        th:nth-child(3), td:nth-child(3) { width: auto; } /* Male Name */
        th:nth-child(4), td:nth-child(4) { width: auto; } /* Female Name */
        th:nth-child(5), td:nth-child(5) { width: auto; } /* Address */
        th:nth-child(6), td:nth-child(6) { width: auto; } /* Remarks */
        th:nth-child(7), td:nth-child(7) { width: auto; }  /* Registered By */
        th:nth-child(8), td:nth-child(8) { width: auto; }  /* Actions */

        /* Add text wrap and auto-fit behavior for name columns */
        td:nth-child(3), td:nth-child(4) {
            white-space: normal;
            overflow-wrap: break-word;
            word-wrap: break-word;
            word-break: normal;
            hyphens: none;
        }

        /* Address column handling */
        td:nth-child(5) {
            white-space: normal;
            overflow-wrap: break-word;
            word-wrap: break-word;
        }

        .family-number {
            font-weight: 600;
            font-size: 14px;
        }

        .family-number-link {
            text-decoration: none;
            color: inherit;
            display: block;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .family-number-link:hover {
            background-color: #4db3a8;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(77, 179, 168, 0.3);
        }

        .family-number-link:hover span {
            color: white !important;
        }

        th {
            background: #4db3a8;
            color: #000;
            font-weight: 600;
        }

        tr:nth-child(even) {
            background: #f8f9fa;
        }

        tr:hover {
            background: #f1f3f4;
        }

        .action-buttons {
            display: flex;
            gap: 0.2rem;
            align-items: center;
            justify-content: flex-start;
        }

        .action-btn {
            padding: 0.35rem;
            width: 24px;
            height: 24px;
            font-size: 0.75rem;
            border-radius: 4px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: relative;
        }

        .edit-btn {
            background: #f8f9fa;
            border-color: #4db3a8;
            color: #4db3a8;
        }

        .edit-btn:hover {
            background: #4db3a8;
            color: #000;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(77, 179, 168, 0.2);
        }

        .print-btn {
            background: #f8f9fa;
            border-color: #3498db;
            color: #3498db;
        }

        .print-btn:hover {
            background: #3498db;
            color: #000;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(52, 152, 219, 0.2);
        }

        .back-btn {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            background: linear-gradient(90deg, #4db3a8, #3a8f87);
            color: #000;
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(77, 179, 168, 0.3);
        }

        .dashboard-btn {
            display: inline-block;
            padding: 0.8rem 1.5rem;
            background: linear-gradient(90deg, #4db3a8, #3a8f87);
            color: #000;
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            margin-left: 1rem;
            font-weight: 600;
        }

        .dashboard-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(77, 179, 168, 0.3);
        }

        .buttons-container {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .no-records {
            text-align: center;
            padding: 2rem;
            color: #000;
            font-size: 1.1rem;
            animation: fadeIn 0.8s ease-out 0.5s forwards;
            opacity: 0;
        }

        .delete-btn {
            background: #f8f9fa;
            border-color: #e74c3c;
            color: #e74c3c;
        }

        .delete-btn:hover {
            background: #e74c3c;
            color: #000;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(231, 76, 60, 0.2);
        }

        .duplicate-name {
            background-color: #fff176;
            padding: 2px 5px;
            border-radius: 3px;
            color: #000;
            font-weight: 600;
            border: 1px dashed #f57f17;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            display: inline-block;
            max-width: 100%;
            word-break: break-word;
        }

        @media (max-width: 1200px) {
            table {
                font-size: 13px;
            }

            th, td {
                padding: 0.6rem 0.4rem;
            }

            .action-btn {
                padding: 0.3rem;
                width: 22px;
                height: 22px;
            }

            /* Adjust name columns on smaller screens */
            th:nth-child(3), td:nth-child(3),
            th:nth-child(4), td:nth-child(4) {
                min-width: 120px;
            }
        }

        @media (max-width: 768px) {
            .table-container {
                margin: 0;
                width: 100%;
                overflow-x: visible;
            }

            table {
                font-size: 12px;
                width: 100%;
                table-layout: auto;
            }

            .search-wrapper {
                flex-direction: column;
            }

            .filter-select {
                width: 100%;
            }
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: relative;
            background: white;
            margin: 15% auto;
            padding: 2rem;
            width: 90%;
            max-width: 500px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            margin-bottom: 1.5rem;
        }

        .modal-header h3 {
            color: #000;
            margin-bottom: 0.5rem;
        }

        .modal-body {
            margin-bottom: 1.5rem;
        }

        .modal-body p {
            color: #000;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            padding-top: 15px;
            border-top: 1px solid #eee;
            margin-top: 15px;
        }

        .modal-btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .cancel-btn {
            background-color: #f1f1f1;
            color: #333;
            margin-right: 10px;
        }

        .confirm-btn {
            background: #e74c3c;
            color: #000;
            font-weight: 600;
        }

        .confirm-btn:hover {
            background: #c0392b;
        }

        .close-modal {
            position: absolute;
            right: 1rem;
            top: 1rem;
            font-size: 1.5rem;
            cursor: pointer;
            color: #7f8c8d;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 2rem;
            margin-bottom: 1rem;
            opacity: 0;
            animation: fadeIn 0.6s ease-out 1s forwards;
        }

        .pagination a, .pagination span {
            padding: 0.5rem 1rem;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            text-decoration: none;
            color: #000;
            transition: all 0.3s ease;
        }

        .pagination a:hover {
            background: #f8f9fa;
            border-color: #4db3a8;
        }

        .pagination .active {
            background: #4db3a8;
            color: #000;
            border-color: #4db3a8;
            font-weight: 600;
        }

        .pagination .disabled {
            color: #000;
            opacity: 0.5;
            pointer-events: none;
        }

        .filter-select {
            padding: 0.8rem 1rem;
            border: 2px solid #ecf0f1;
            border-radius: 12px;
            font-size: 1rem;
            color: #000;
            background: #f8f9fa;
            min-width: 200px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: #4db3a8;
            box-shadow: 0 0 0 3px rgba(77, 179, 168, 0.1);
        }

        /* Animation styles */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes scaleIn {
            from {
                transform: scale(0.95);
                opacity: 0;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes shimmer {
            100% {
                left: 100%;
            }
        }

        /* Add missing slideInFromLeft animation */
        @keyframes slideInFromLeft {
            from {
                width: 0;
            }
            to {
                /* Width is dynamically set inline */
            }
        }

        /* New animations for duplicates view */
        @keyframes highlight {
            0%, 100% { background-color: rgba(255, 193, 7, 0.1); }
            50% { background-color: rgba(255, 193, 7, 0.3); }
        }

        @keyframes pulseScale {
            0% { transform: scale(1); }
            50% { transform: scale(1.02); }
            100% { transform: scale(1); }
        }

        @keyframes borderPulse {
            0% { border-color: rgba(255, 193, 7, 0.5); }
            50% { border-color: rgba(255, 193, 7, 1); }
            100% { border-color: rgba(255, 193, 7, 0.5); }
        }

        @keyframes fadeUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Enhanced duplicates view styling */
        .duplicates-view .table-container {
            max-height: none;
            overflow-y: visible;
        }

        /* Special animation for duplicates warning banner */
        .duplicates-view .header div {
            animation: pulseScale 3s infinite ease-in-out;
        }

        /* Staggered animations for duplicate records table */
        .duplicates-view table {
            animation: fadeIn 0.6s ease-out forwards;
        }

        .duplicates-view tbody tr {
            animation: fadeUp 0.7s ease-out forwards;
            position: relative;
            border-left: 3px solid transparent;
        }

        /* More staggered steps for duplicates view to accommodate more rows */
        .duplicates-view tbody tr:nth-child(1) { animation-delay: 0.1s; }
        .duplicates-view tbody tr:nth-child(2) { animation-delay: 0.15s; }
        .duplicates-view tbody tr:nth-child(3) { animation-delay: 0.2s; }
        .duplicates-view tbody tr:nth-child(4) { animation-delay: 0.25s; }
        .duplicates-view tbody tr:nth-child(5) { animation-delay: 0.3s; }
        .duplicates-view tbody tr:nth-child(6) { animation-delay: 0.35s; }
        .duplicates-view tbody tr:nth-child(7) { animation-delay: 0.4s; }
        .duplicates-view tbody tr:nth-child(8) { animation-delay: 0.45s; }
        .duplicates-view tbody tr:nth-child(9) { animation-delay: 0.5s; }
        .duplicates-view tbody tr:nth-child(10) { animation-delay: 0.55s; }
        .duplicates-view tbody tr:nth-child(11) { animation-delay: 0.6s; }
        .duplicates-view tbody tr:nth-child(12) { animation-delay: 0.65s; }
        .duplicates-view tbody tr:nth-child(13) { animation-delay: 0.7s; }
        .duplicates-view tbody tr:nth-child(14) { animation-delay: 0.75s; }
        .duplicates-view tbody tr:nth-child(15) { animation-delay: 0.8s; }
        .duplicates-view tbody tr:nth-child(16) { animation-delay: 0.85s; }
        .duplicates-view tbody tr:nth-child(17) { animation-delay: 0.9s; }
        .duplicates-view tbody tr:nth-child(18) { animation-delay: 0.95s; }
        .duplicates-view tbody tr:nth-child(19) { animation-delay: 1.0s; }
        .duplicates-view tbody tr:nth-child(20) { animation-delay: 1.05s; }

        /* Enhanced highlight for duplicate values */
        .duplicates-view .duplicate-name {
            background-color: rgba(255, 193, 7, 0.15);
            border-radius: 4px;
            padding: 3px 6px;
            position: relative;
            display: inline-block;
            animation: highlight 2s infinite ease-in-out;
            border: 1px solid rgba(255, 193, 7, 0.5);
        }

        /* Add animation to the cell containing duplicate values */
        .duplicates-view td span.duplicate-name {
            animation: borderPulse 2s infinite ease-in-out, highlight 2s infinite ease-in-out;
        }

        /* Add hover effect on duplicate items */
        .duplicates-view .duplicate-name:hover {
            background-color: rgba(255, 193, 7, 0.3);
            transform: scale(1.05);
            transition: all 0.3s ease;
            cursor: pointer;
            z-index: 10;
        }

        /* Enhance header styling in duplicates view */
        .duplicates-view th {
            background: linear-gradient(45deg, #4db3a8, #3a8f87);
            color: #000;
            font-weight: 600;
            position: relative;
            overflow: hidden;
            animation: fadeIn 0.5s ease-out forwards;
        }

        .duplicates-view th::after {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 2s infinite;
        }

        /* Enhanced row hover effect for duplicates view */
        .duplicates-view tbody tr:hover {
            background-color: rgba(77, 179, 168, 0.1);
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.05);
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }

        /* Apply animations to elements */
        .container {
            animation: scaleIn 0.5s ease-out forwards;
            position: relative;
            overflow: hidden;
        }

        .header {
            animation: fadeIn 0.6s ease-out forwards;
        }

        .search-container {
            animation: fadeIn 0.7s ease-out forwards;
        }

        .search-btn, .clear-search {
            position: relative;
            overflow: hidden;
        }

        .search-btn::after, .clear-search::after {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 2s infinite;
        }

        table thead {
            animation: fadeIn 0.6s ease-out forwards;
        }

        tbody tr {
            opacity: 0;
            animation: fadeInRight 0.5s ease-out forwards;
        }

        /* Staggered animation for table rows */
        tbody tr:nth-child(1) { animation-delay: 0.3s; }
        tbody tr:nth-child(2) { animation-delay: 0.4s; }
        tbody tr:nth-child(3) { animation-delay: 0.5s; }
        tbody tr:nth-child(4) { animation-delay: 0.6s; }
        tbody tr:nth-child(5) { animation-delay: 0.7s; }
        tbody tr:nth-child(6) { animation-delay: 0.8s; }
        tbody tr:nth-child(7) { animation-delay: 0.9s; }
        tbody tr:nth-child(8) { animation-delay: 1.0s; }
        tbody tr:nth-child(9) { animation-delay: 1.1s; }
        tbody tr:nth-child(10) { animation-delay: 1.2s; }

        .pagination {
            opacity: 0;
            animation: fadeIn 0.6s ease-out 1s forwards;
        }

        .action-btn {
            transition: all 0.3s ease;
            position: relative;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .no-records {
            animation: fadeIn 0.8s ease-out 0.5s forwards;
            opacity: 0;
        }

        /* Delete animation styles */
        .deleting-message {
            text-align: center;
            padding: 12px;
            color: #666;
            font-style: italic;
        }

        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-top-color: #4db3a8;
            animation: spin 0.8s linear infinite;
            vertical-align: middle;
            margin-right: 8px;
        }

        .success-message {
            text-align: center;
            padding: 12px;
            color: #4db3a8;
            font-weight: bold;
            background-color: rgba(77, 179, 168, 0.1);
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
                transform: translateY(0);
            }
            to {
                opacity: 0;
                transform: translateY(-20px);
            }
        }

        /* Add the following styles to your CSS section */
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .cancel-btn {
            background-color: #f1f1f1;
            color: #333;
            margin-right: 10px;
        }

        .delete-btn {
            background-color: #e74c3c;
            color: white;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            padding-top: 15px;
            border-top: 1px solid #eee;
            margin-top: 15px;
        }
    </style>

    <script>
        let recordIdToDelete = null;

        function openDeleteModal(id) {
            recordIdToDelete = id;
            document.getElementById('deleteModal').style.display = 'block';
            if (document.getElementById('deleteReason')) {
                document.getElementById('deleteReason').value = ''; // Clear any previous reason
            }
            resetTimeout(); // Reset timeout when modal is opened
        }

        function closeModal() {
            document.getElementById('deleteModal').style.display = 'none';
            resetTimeout(); // Reset timeout when modal is closed
        }

        // Timeout functionality
        let inactivityTimeout;

        function resetTimeout() {
            clearTimeout(inactivityTimeout);
            inactivityTimeout = setTimeout(function() {
                window.location.href = 'dashboard.php';
            }, 300000); // 5 minutes timeout
        }

        // Initialize timeout when page loads
        document.addEventListener('DOMContentLoaded', function() {
            resetTimeout();

            // Reset timeout on user activity
            document.addEventListener('mousemove', resetTimeout);
            document.addEventListener('keypress', resetTimeout);
            document.addEventListener('click', resetTimeout);
            document.addEventListener('scroll', resetTimeout);

            // Add event listeners to form elements to reset timeout
            const formElements = document.querySelectorAll('input, select, textarea, button');
            formElements.forEach(element => {
                element.addEventListener('focus', resetTimeout);
                element.addEventListener('change', resetTimeout);
            });

            // Check if this is the duplicates view
            const urlParams = new URLSearchParams(window.location.search);
            const isDuplicatesView = urlParams.get('duplicates') === '1';

            // Enhance table data animations
            const addDataAnimations = () => {
                // Add subtle pulse animation to table cells
                document.querySelectorAll('tbody td').forEach((cell, index) => {
                    // Skip action buttons column
                    if (!cell.classList.contains('action-buttons')) {
                        // Add hover effect
                        cell.addEventListener('mouseenter', function() {
                            this.style.backgroundColor = 'rgba(77, 179, 168, 0.1)';
                            this.style.transition = 'all 0.3s ease';
                        });

                        cell.addEventListener('mouseleave', function() {
                            this.style.backgroundColor = '';
                        });

                        // Add initial fade-in animation with staggered delay based on column
                        const colIndex = index % 8; // Assuming 8 columns maximum
                        cell.style.opacity = '0';
                        cell.style.animation = `fadeIn 0.5s ease-out ${0.1 + (colIndex * 0.05)}s forwards`;
                    }
                });

                // Add row highlight effect on hover
                document.querySelectorAll('tbody tr').forEach(row => {
                    row.addEventListener('mouseenter', function() {
                        this.style.transform = 'translateY(-2px)';
                        this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
                        this.style.transition = 'all 0.3s ease';
                    });

                    row.addEventListener('mouseleave', function() {
                        this.style.transform = '';
                        this.style.boxShadow = '';
                    });
                });

                // Add animation to cell content
                document.querySelectorAll('.family-number').forEach(cell => {
                    cell.addEventListener('click', function() {
                        this.style.animation = 'pulseScale 0.5s ease';
                        setTimeout(() => {
                            this.style.animation = '';
                        }, 500);
                    });
                });
            };

            // Call animation function
            setTimeout(addDataAnimations, 300);

            if (isDuplicatesView) {
                // Add the duplicates-view class to the container
                document.querySelector('.container').classList.add('duplicates-view');

                // Add special animation to duplicate items
                const duplicateElements = document.querySelectorAll('.duplicate-name');
                duplicateElements.forEach((element, index) => {
                    // Add pulsing border animation with staggered delays
                    element.style.animation = `borderPulse 3s infinite ${index * 0.2}s ease-in-out`;

                    // Add highlight on hover
                    element.addEventListener('mouseenter', function() {
                        this.style.animation = 'highlight 1s infinite ease-in-out';
                        this.style.cursor = 'pointer';

                        // Find all elements with the same content and highlight them
                        const value = this.textContent.trim();
                        document.querySelectorAll('.duplicate-name').forEach(el => {
                            if (el.textContent.trim() === value) {
                                el.style.animation = 'highlight 1s infinite ease-in-out';
                                el.style.backgroundColor = 'rgba(255, 193, 7, 0.3)';
                            }
                        });
                    });

                    element.addEventListener('mouseleave', function() {
                        this.style.animation = `borderPulse 3s infinite ${index * 0.2}s ease-in-out`;

                        // Reset all elements with the same content
                        const value = this.textContent.trim();
                        document.querySelectorAll('.duplicate-name').forEach(el => {
                            if (el.textContent.trim() === value) {
                                el.style.animation = `borderPulse 3s infinite ${index * 0.2}s ease-in-out`;
                                el.style.backgroundColor = '';
                            }
                        });
                    });
                });

                // Add animated border for rows with duplicates
                const tableRows = document.querySelectorAll('tbody tr');
                tableRows.forEach((row) => {
                    if (row.querySelectorAll('.duplicate-name').length > 0) {
                        row.style.animation = 'fadeUp 0.7s ease-out forwards';
                        row.style.borderLeft = '3px solid #f39c12';
                    }
                });
            }
        });

        function confirmDelete() {
            if (!recordIdToDelete) {
                console.error('No record ID to delete');
                return;
            }

            console.log('Deleting record ID:', recordIdToDelete);

            // Get the delete reason
            const deleteReason = document.getElementById('deleteReason').value.trim();

            // Validate that a reason is provided
            if (!deleteReason) {
                alert('Please provide a reason for deletion');
                return;
            }

            // Find the row by data-id attribute
            const row = document.querySelector(`tr[data-id="${recordIdToDelete}"]`);
            if (!row) {
                console.error('Could not find row with ID:', recordIdToDelete);
                alert('Error: Could not find the record row');
                return;
            }

            // Show deleting status
            const originalContent = row.innerHTML;
            row.innerHTML = '<td colspan="8" class="deleting-message"><div class="spinner"></div> Deleting...</td>';

            // Create form data
            const formData = new FormData();
            formData.append('id', recordIdToDelete);
            formData.append('reason', deleteReason);

            // Log the form data for debugging
            console.log('Form data keys:', [...formData.keys()]);
            console.log('Form data entries:', [...formData.entries()].map(e => `${e[0]}: ${e[1]}`).join(', '));

            // Send delete request
            fetch('delete_family.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.text().then(text => {
                    console.log('Raw response:', text);
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('Error parsing response:', text);
                        return { status: 'error', message: 'Invalid server response: ' + text };
                    }
                });
            })
            .then(result => {
                console.log('Parsed result:', result);
                if (result.status === 'success') {
                    // Show success message briefly
                    row.innerHTML = '<td colspan="8" class="success-message"><i class="fas fa-check-circle"></i> Record deleted successfully!</td>';

                    // Add fade-out animation
                    setTimeout(() => {
                        row.style.animation = 'fadeOut 0.5s ease forwards';

                        // Remove row after animation completes
                        setTimeout(() => {
                            row.remove();

                            // If no more rows, show no records message
                            if (document.querySelectorAll('tbody tr').length === 0) {
                                const table = document.querySelector('table');
                                const noRecords = document.createElement('div');
                                noRecords.className = 'no-records';
                                noRecords.innerHTML = '<i class="fas fa-info-circle"></i> No records found.';
                                table.after(noRecords);
                                table.style.display = 'none';
                            }
                        }, 500);
                    }, 1000);
                } else {
                    // Show error and restore row
                    console.error('Delete error:', result.message);
                    alert('Error deleting family record: ' + (result.message || 'Unknown error'));
                    row.innerHTML = originalContent;
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                alert('Error processing the delete request: ' + error.message);
                row.innerHTML = originalContent;
            });

            // Close the modal
            closeModal();
            resetTimeout(); // Reset timeout during delete operation
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('deleteModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // Initialize dropdown functionality for sidebar
        document.addEventListener('DOMContentLoaded', function() {
            const dropdownBtns = document.querySelectorAll('.dropdown-btn');

            dropdownBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.classList.toggle('active');
                    const dropdownContent = this.nextElementSibling;
                    dropdownContent.classList.toggle('show');
                });
            });
        });

        // Rest of your existing JavaScript...
    </script>
</head>
<body>
    <div class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="./images/brgy_la_huerta.png" alt="Barangay La Huerta Logo">
                <h2>Family Number Management System</h2>
            </div>
            <ul class="nav-menu">
                <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super admin'): ?>
                    <li class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <i class="fas fa-home"></i>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="family_registration.php" class="nav-link">
                            <i class="fas fa-user-plus"></i>
                            Register Family
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link dropdown-btn <?php echo isset($_GET['duplicates']) || !isset($_GET['duplicates']) ? 'active' : ''; ?>">
                            <i class="fas fa-folder"></i>
                            Records
                        </a>
                        <div class="dropdown-container show">
                            <a href="view_family_records.php" class="dropdown-link <?php echo !isset($_GET['duplicates']) ? 'active' : ''; ?>">
                                <i class="fas fa-table"></i>
                                View Records
                            </a>
                            <a href="view_family_records.php?duplicates=1" class="dropdown-link <?php echo isset($_GET['duplicates']) ? 'active' : ''; ?>">
                                <i class="fas fa-exclamation-triangle"></i>
                                Duplicate Records
                            </a>
                            <?php if ($_SESSION['role'] === 'super admin'): ?>
                            <a href="missing_family_numbers.php" class="dropdown-link">
                                <i class="fas fa-list-ol"></i>
                                Missing Family Numbers
                            </a>
                            <a href="recently_deleted.php" class="dropdown-link">
                                <i class="fas fa-trash-alt"></i>
                                Recently Deleted
                            </a>
                            <?php endif; ?>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a href="scanner.php" class="nav-link">
                            <i class="fas fa-qrcode"></i>
                            Scan Family ID
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="manage_users.php" class="nav-link">
                            <i class="fas fa-users"></i>
                            Manage Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="logout.php" class="nav-link">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </a>
                    </li>
                <?php else: ?>
                    <li class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <i class="fas fa-home"></i>
                            Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="family_registration.php" class="nav-link">
                            <i class="fas fa-user-plus"></i>
                            Register Family
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link dropdown-btn <?php echo isset($_GET['duplicates']) || !isset($_GET['duplicates']) ? 'active' : ''; ?>">
                            <i class="fas fa-folder"></i>
                            Records
                        </a>
                        <div class="dropdown-container show">
                            <a href="view_family_records.php" class="dropdown-link <?php echo !isset($_GET['duplicates']) ? 'active' : ''; ?>">
                                <i class="fas fa-table"></i>
                                View Records
                            </a>
                            <a href="view_family_records.php?duplicates=1" class="dropdown-link <?php echo isset($_GET['duplicates']) ? 'active' : ''; ?>">
                                <i class="fas fa-exclamation-triangle"></i>
                                Duplicate Records
                            </a>
                            <?php if ($_SESSION['role'] === 'super admin'): ?>
                            <a href="missing_family_numbers.php" class="dropdown-link">
                                <i class="fas fa-list-ol"></i>
                                Missing Family Numbers
                            </a>
                            <a href="recently_deleted.php" class="dropdown-link">
                                <i class="fas fa-trash-alt"></i>
                                Recently Deleted
                            </a>
                            <?php endif; ?>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a href="scanner.php" class="nav-link">
                            <i class="fas fa-qrcode"></i>
                            Scan Family ID
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="logout.php" class="nav-link">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>

        <div class="main-content">
            <div class="container">
                <div class="header">
                    <h2>FAMILY RECORDS</h2>
                    <?php if ($duplicates_filter): ?>
                        <div style="margin-top: 10px; background-color: #fffbd0; padding: 10px; border-radius: 8px; border-left: 4px solid #f39c12;">
                            <p style="margin: 0; color: #000; display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-exclamation-triangle" style="color: #f39c12;"></i>
                                Showing records with duplicate family numbers, male names & female names
                            </p>
                            <p style="margin-top: 5px; font-size: 0.9em; color: #555;">
                                <i class="fas fa-info-circle"></i> Highlighted cells indicate duplicate values that appear in multiple records.
                            </p>
                        </div>

                        <!-- Duplicates Summary Section with Animations -->
                        <div class="duplicates-summary" style="margin-top: 20px; display: flex; justify-content: space-between; flex-wrap: wrap; gap: 15px; opacity: 0; animation: fadeIn 0.8s ease-out 0.5s forwards;">
                            <?php
                            // Count duplicates for each category
                            $duplicateFamilyNumbersCount = count($duplicate_family_numbers);
                            $duplicateMaleNamesCount = count($duplicate_male_names);
                            $duplicateFemaleNamesCount = count($duplicate_female_names);

                            // Total duplicates
                            $totalDuplicatesCount = $duplicateFamilyNumbersCount + $duplicateMaleNamesCount + $duplicateFemaleNamesCount;
                            ?>

                            <div class="duplicate-stat-card" style="flex: 1; min-width: 200px; background: white; padding: 15px; border-radius: 8px; box-shadow: 0 3px 10px rgba(0,0,0,0.08); border-left: 4px solid #e74c3c; opacity: 0; animation: fadeIn 0.5s ease-out 0.6s forwards, pulseScale 3s infinite ease-in-out;">
                                <h3 style="margin: 0 0 8px 0; color: #333; font-size: 1rem; display: flex; align-items: center; gap: 8px;">
                                    <i class="fas fa-hashtag" style="color: #e74c3c;"></i> Duplicate Family Numbers
                                </h3>
                                <div style="display: flex; align-items: baseline; gap: 10px;">
                                    <span style="font-size: 1.8rem; font-weight: 700; color: #e74c3c;"><?php echo $duplicateFamilyNumbersCount; ?></span>
                                    <span style="color: #666; font-size: 0.9rem;">unique values with duplicates</span>
                                </div>
                                <div class="progress-bar" style="margin-top: 10px; height: 6px; background: #f0f0f0; border-radius: 3px; overflow: hidden; position: relative;">
                                    <div style="position: absolute; top: 0; left: 0; height: 100%; width: <?php echo ($duplicateFamilyNumbersCount / max(1, $totalDuplicatesCount)) * 100; ?>%; background: #e74c3c; border-radius: 3px; animation: slideInFromLeft 1s ease-out;"></div>
                                </div>
                            </div>

                            <div class="duplicate-stat-card" style="flex: 1; min-width: 200px; background: white; padding: 15px; border-radius: 8px; box-shadow: 0 3px 10px rgba(0,0,0,0.08); border-left: 4px solid #3498db; opacity: 0; animation: fadeIn 0.5s ease-out 0.8s forwards, pulseScale 3s infinite ease-in-out 0.2s;">
                                <h3 style="margin: 0 0 8px 0; color: #333; font-size: 1rem; display: flex; align-items: center; gap: 8px;">
                                    <i class="fas fa-mars" style="color: #3498db;"></i> Duplicate Male Names
                                </h3>
                                <div style="display: flex; align-items: baseline; gap: 10px;">
                                    <span style="font-size: 1.8rem; font-weight: 700; color: #3498db;"><?php echo $duplicateMaleNamesCount; ?></span>
                                    <span style="color: #666; font-size: 0.9rem;">unique names with duplicates</span>
                                </div>
                                <div class="progress-bar" style="margin-top: 10px; height: 6px; background: #f0f0f0; border-radius: 3px; overflow: hidden; position: relative;">
                                    <div style="position: absolute; top: 0; left: 0; height: 100%; width: <?php echo ($duplicateMaleNamesCount / max(1, $totalDuplicatesCount)) * 100; ?>%; background: #3498db; border-radius: 3px; animation: slideInFromLeft 1s ease-out 0.2s;"></div>
                                </div>
                            </div>

                            <div class="duplicate-stat-card" style="flex: 1; min-width: 200px; background: white; padding: 15px; border-radius: 8px; box-shadow: 0 3px 10px rgba(0,0,0,0.08); border-left: 4px solid #9b59b6; opacity: 0; animation: fadeIn 0.5s ease-out 1s forwards, pulseScale 3s infinite ease-in-out 0.4s;">
                                <h3 style="margin: 0 0 8px 0; color: #333; font-size: 1rem; display: flex; align-items: center; gap: 8px;">
                                    <i class="fas fa-venus" style="color: #9b59b6;"></i> Duplicate Female Names
                                </h3>
                                <div style="display: flex; align-items: baseline; gap: 10px;">
                                    <span style="font-size: 1.8rem; font-weight: 700; color: #9b59b6;"><?php echo $duplicateFemaleNamesCount; ?></span>
                                    <span style="color: #666; font-size: 0.9rem;">unique names with duplicates</span>
                                </div>
                                <div class="progress-bar" style="margin-top: 10px; height: 6px; background: #f0f0f0; border-radius: 3px; overflow: hidden; position: relative;">
                                    <div style="position: absolute; top: 0; left: 0; height: 100%; width: <?php echo ($duplicateFemaleNamesCount / max(1, $totalDuplicatesCount)) * 100; ?>%; background: #9b59b6; border-radius: 3px; animation: slideInFromLeft 1s ease-out 0.4s;"></div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="search-container">
                    <form method="GET" action="" class="search-form">
                        <div class="search-wrapper">
                            <input
                                type="text"
                                class="search-input"
                                name="search"
                                placeholder="Search by any field..."
                                value="<?php echo htmlspecialchars($search); ?>"
                            >
                            <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super admin'): ?>
                                <select name="registered_by" class="filter-select">
                                    <option value="">All Registrants</option>
                                    <?php foreach ($registered_by_users as $user): ?>
                                        <option value="<?php echo htmlspecialchars($user); ?>"
                                                <?php echo $registered_by_filter === $user ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars(strtoupper($user)); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            <?php endif; ?>
                            <?php if ($duplicates_filter): ?>
                                <input type="hidden" name="duplicates" value="1">
                            <?php endif; ?>
                            <button type="submit" class="search-btn">
                                <i class="fas fa-search"></i> Search
                            </button>
                            <?php if (!empty($search) || !empty($registered_by_filter) || $duplicates_filter): ?>
                                <a href="?" class="clear-search">
                                    <i class="fas fa-times"></i> Clear All Filters
                                </a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
                <div class="table-container">
                    <?php if ($result && $result->num_rows > 0): ?>
                        <table>
                            <thead>
                                <tr>
                                    <th>Registration Date</th>
                                    <th>Family Number</th>
                                    <th>Male Name</th>
                                    <th>Female Name</th>
                                    <th>Address</th>
                                    <th>Remarks</th>
                                    <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super admin'): ?>
                                        <th>Registered By</th>
                                    <?php endif; ?>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($row = $result->fetch_assoc()):
                                    // Check if names are duplicates
                                    $male_name_class = in_array($row['male_name'], $duplicate_male_names) ? 'duplicate-name' : '';
                                    $female_name_class = in_array($row['female_name'], $duplicate_female_names) ? 'duplicate-name' : '';
                                    $family_number_class = in_array($row['family_number'], $duplicate_family_numbers) ? 'duplicate-name' : '';
                                ?>
                                    <tr data-id="<?php echo $row['id']; ?>">
                                        <td><?php echo date('M d, Y', strtotime($row['registration_date'])); ?></td>
                                        <td class="family-number">
                                            <a href="view_family_detail.php?id=<?php echo $row['id']; ?>" class="family-number-link">
                                                <span class="<?php echo $family_number_class; ?>"><?php echo htmlspecialchars($row['family_number']); ?></span>
                                            </a>
                                        </td>
                                        <td><span class="<?php echo $male_name_class; ?>"><?php echo htmlspecialchars($row['male_name']); ?></span></td>
                                        <td><span class="<?php echo $female_name_class; ?>"><?php echo htmlspecialchars($row['female_name']); ?></span></td>
                                        <td><?php echo htmlspecialchars($row['address']); ?></td>
                                        <td><?php echo htmlspecialchars($row['remarks']); ?></td>
                                        <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super admin'): ?>
                                            <td><?php echo htmlspecialchars(strtoupper($row['registered_by'])); ?></td>
                                        <?php endif; ?>
                                        <td class="action-buttons">
                                            <a href="edit_family.php?id=<?php echo $row['id']; ?>" class="action-btn edit-btn" title="Edit Record">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="print_family.php?id=<?php echo $row['id']; ?>" class="action-btn print-btn" title="Print Record" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super admin'): ?>
                                            <button type="button" onclick="openDeleteModal(<?php echo $row['id']; ?>)" class="action-btn delete-btn" title="Delete Record">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>

                        <!-- Pagination -->
                        <?php if (!$duplicates_filter && $total_pages > 1): ?>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?page=1<?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($registered_by_filter) ? '&registered_by=' . urlencode($registered_by_filter) : ''; ?>">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                                <a href="?page=<?php echo ($page - 1) . (!empty($search) ? '&search=' . urlencode($search) : '') . (!empty($registered_by_filter) ? '&registered_by=' . urlencode($registered_by_filter) : ''); ?>">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            <?php endif; ?>

                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);

                            for ($i = $start_page; $i <= $end_page; $i++): ?>
                                <a href="?page=<?php echo $i . (!empty($search) ? '&search=' . urlencode($search) : '') . (!empty($registered_by_filter) ? '&registered_by=' . urlencode($registered_by_filter) : ''); ?>"
                                   class="<?php echo $i === $page ? 'active' : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>

                            <?php if ($page < $total_pages): ?>
                                <a href="?page=<?php echo ($page + 1) . (!empty($search) ? '&search=' . urlencode($search) : '') . (!empty($registered_by_filter) ? '&registered_by=' . urlencode($registered_by_filter) : ''); ?>">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                                <a href="?page=<?php echo $total_pages . (!empty($search) ? '&search=' . urlencode($search) : '') . (!empty($registered_by_filter) ? '&registered_by=' . urlencode($registered_by_filter) : ''); ?>">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="no-records">
                            <i class="fas fa-info-circle"></i> No records found.
                        </div>
                    <?php endif; ?>
                <footer style="margin-top: -0px; padding: 10px; text-align: center; border-top: 1px solid #ddd; font-size: 13px; color: #666;">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <span>&copy; <?php echo date("Y"); ?> Family Number Management System</span>
        <span style="margin-right: 10px;">HIMU-Justin</span>
    </div>
</footer>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <span class="close-modal" onclick="closeModal()">&times;</span>
            <div class="modal-header">
                <h3>Confirm Delete</h3>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this family record? This action can be undone from the 'Recently Deleted' page.</p>
                <div class="form-group" style="margin-top: 15px;">
                    <label for="deleteReason">Reason for deletion: <span style="color: #e74c3c;">*</span></label>
                    <textarea id="deleteReason" rows="3" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-family: inherit; resize: vertical;"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="closeModal()" class="btn cancel-btn">Cancel</button>
                <button type="button" onclick="confirmDelete()" class="btn delete-btn">Delete</button>
            </div>
        </div>
    </div>

</body>
</html>
<?php $conn->close(); ?>
