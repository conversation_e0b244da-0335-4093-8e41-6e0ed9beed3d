    <?php
    session_start();
    if (!isset($_SESSION['username'])) {
        header("Location: login.php");
        exit();
    }

    include "db.php";

    // Run inactive user check (for users who haven't logged in for 3 months)
    if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super admin') {
        // First check if last_login column exists
        $check_column = $conn->query("SHOW COLUMNS FROM users LIKE 'last_login'");
        if ($check_column->num_rows > 0) {
            $inactive_check_sql = "UPDATE users
                                  SET status = 'inactive'
                                  WHERE
                                      role IN ('admin', 'staff')
                                      AND status = 'active'
                                      AND last_login IS NOT NULL
                                      AND last_login < DATE_SUB(NOW(), INTERVAL 3 MONTH)";
            $conn->query($inactive_check_sql);
        }
    }

    // Get statistics
    $stats = [
        'total_families' => $conn->query("SELECT COUNT(*) as count FROM family_registration")->fetch_assoc()['count'],
        'total_users' => $conn->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'],
        'recent_registrations' => $conn->query("SELECT COUNT(*) as count FROM family_registration WHERE registration_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)")->fetch_assoc()['count']
    ];
    ?>

    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Dashboard - Family Registration System</title>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
                font-family: 'Lexend', sans-serif;
            }

            body {
                min-height: 100vh;
                background: #f0f7ff;
            }

            .dashboard {
                display: flex;
                min-height: 100vh;
            }

            .sidebar {
                width: 250px;
                background: #4db3a8;
                color: white;
                padding: 2rem 0;
            }

            .sidebar-header {
                padding: 0 2rem;
                margin-bottom: 2rem;
                text-align: center;
            }

            .sidebar-header img {
                width: 180px;
                height: 180px;
                margin-bottom: 1.5rem;
                border-radius: 50%;
                object-fit: contain;
                padding: 10px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            }

            .sidebar-header h2 {
                font-size: 1.5rem;
                font-weight: 600;
                color: #000;
                line-height: 1.3;
            }

            .nav-menu {
                list-style: none;
            }

            .nav-item {
                margin-bottom: 0.5rem;
            }

            .nav-link {
                display: flex;
                align-items: center;
                padding: 1rem 2rem;
                color: #000;
                text-decoration: none;
                transition: all 0.3s ease;
            }

            .nav-link:hover, .nav-link.active {
                background: #3a8f87;
                color: #000;
            }

            .nav-link i {
                margin-right: 1rem;
                width: 20px;
                color: #000;
            }

            /* Dropdown styles */
            .dropdown-container {
                display: none;
                padding-left: 1rem;
            }

            .dropdown-link {
                display: flex;
                align-items: center;
                padding: 0.8rem 2rem;
                color: #000;
                text-decoration: none;
                transition: all 0.3s ease;
            }

            .dropdown-link:hover {
                background: #3a8f87;
                color: #000;
            }

            .dropdown-link i {
                margin-right: 1rem;
                width: 20px;
                color: #000;
            }

            .nav-link.dropdown-btn {
                cursor: pointer;
            }

            .dropdown-btn::after {
                content: '\f107';
                font-family: 'Font Awesome 5 Free';
                font-weight: 900;
                margin-left: auto;
                transition: transform 0.3s;
            }

            .dropdown-btn.active::after {
                transform: rotate(180deg);
            }
            
            .dropdown-container.show {
                display: block;
            }
            
            /* Animation for dropdown */
            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .dropdown-container.show {
                animation: slideDown 0.3s ease-out forwards;
            }

            .main-content {
                flex: 1;
                padding: 2rem;
            }

            .header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 2rem;
                flex-direction: row;
            }

            .welcome-text {
                min-width: 240px;
                font-size: 1.1rem;
                color: #222;
                font-weight: 700;
                letter-spacing: 0.5px;
                margin-right: auto;
            }

            .date-time-container {
                display: flex;
                align-items: center;
                font-size: 1.1rem;
                color: #222;
                font-weight: 700;
                letter-spacing: 0.5px;
                min-width: 240px;
                margin: 0 0 0 1.5rem;
                justify-content: flex-end;
                background: white;
                padding: 0.5rem 1rem;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            }

            .date-time-container i {
                margin-right: 0.5rem;
                color: #4db3a8;
            }

            .user-info {
                display: flex;
                align-items: center;
                gap: 1rem;
                padding: 0.5rem;
                border-radius: 12px;
            }

            .logo-container {
                background: white;
                padding: 0.5rem;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .user-info img {
                width: 50px;
                height: 50px;
                object-fit: contain;
                transition: transform 0.3s ease;
            }

            .user-info img:hover {
                transform: scale(1.1);
            }

            .user-info span {
                color: #000;
            }

            .logout-btn {
                padding: 0.5rem 1rem;
                background: #e74c3c;
                color: #000;
                text-decoration: none;
                border-radius: 8px;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                transition: all 0.3s ease;
                font-weight: 600;
            }

            .logout-btn:hover {
                background: #c0392b;
            }

            .stats-container {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 0.5rem;
                margin-bottom: 2rem;
            }

            .stat-card {
                background: white;
                padding: 2rem;
                border-radius: 15px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
                text-align: center;
                transition: transform 0.3s ease;
                border: 1px solid rgba(0, 0, 0, 0.05);
                position: relative;
                overflow: hidden;
            }

            .stat-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 4px;
                background: linear-gradient(90deg, #4db3a8, #3a8f87);
            }

            .stat-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 6px 20px rgba(77, 179, 168, 0.1);
            }

            .stat-card i {
                font-size: 2.5rem;
                margin-bottom: 1rem;
                color: #4db3a8;
            }

            .stat-card h3 {
                font-size: 2rem;
                color: #000;
                margin-bottom: 0.5rem;
                font-weight: 600;
            }

            .stat-card h3.latest-number {
                font-weight: 700;
                color:rgb(31, 10, 218);
                text-shadow: 1px 1px 2px rgba(77, 179, 168, 0.2);
            }

            .stat-card p {
                color: #000;
                font-size: 1rem;
                margin: 0;
                font-weight: 500;
            }

            .quick-actions {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(230px, 1fr));
                gap: 1rem;
                margin-bottom: 2rem;
            }
            .wide-action-card {
                grid-column: span 2;
            }

            .graph-container {
                background: white;
                padding: 2rem;
                border-radius: 15px;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
                margin-top: 2rem;
            }

            .graph-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1.5rem;
            }

            .graph-container h2 {
                color: #000;
                font-size: 1.5rem;
                font-weight: 600;
            }

            .period-selector {
                padding: 0.5rem 1rem;
                border: 2px solid #4db3a8;
                border-radius: 8px;
                font-size: 1rem;
                color: #000;
                background: white;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .period-selector:focus {
                outline: none;
                box-shadow: 0 0 0 3px rgba(77, 179, 168, 0.1);
            }

            .period-selector option {
                color: #000;
            }

            .graph-wrapper {
                position: relative;
                height: 400px;
            }

            .action-card {
                background: white;
                padding: 1.5rem;
                border-radius: 12px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
                text-decoration: none;
                color: #000;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                height: 100%;
            }

            .action-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 4px;
                background: linear-gradient(90deg, #4db3a8, #3a8f87);
            }

            .action-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 5px 15px rgba(77, 179, 168, 0.1);
            }

            .action-card h3 {
                margin-bottom: 1rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                color: #000;
            }

            .action-card p {
                color: #000;
                font-size: 0.9rem;
            }

            @media (max-width: 768px) {
                .dashboard {
                    flex-direction: column;
                }

                .sidebar {
                    width: 100%;
                    padding: 1rem 0;
                }

                .main-content {
                    padding: 1rem;
                }

                .stats-container {
                    grid-template-columns: 1fr;
                }

                .header {
                    flex-direction: column;
                    gap: 1rem;
                }

                .user-info {
                    width: 100%;
                    justify-content: center;
                    flex-wrap: wrap;
                }

                .user-info img {
                    width: 40px;
                    height: 40px;
                }
            }

            .import-btn {
                background: linear-gradient(90deg,hsl(174, 40.20%, 50.20%), #008000);
                color: #000;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 1rem;
                transition: all 0.3s ease;
                font-weight: 600;
            }

            .import-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px #4db383;
            }

            .modal {
                display: none;
                position: fixed;
                z-index: 1;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0,0,0,0.4);
            }

            .modal-content {
                background-color: #fefefe;
                margin: 15% auto;
                padding: 20px;
                border: 1px solid #888;
                width: 80%;
                max-width: 500px;
                border-radius: 12px;
                position: relative;
            }

            .modal-content::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 4px;
                background: linear-gradient(90deg, #4db3a8, #3a8f87);
                border-radius: 12px 12px 0 0;
            }

            .close {
                color: #aaa;
                float: right;
                font-size: 28px;
                font-weight: bold;
                cursor: pointer;
            }

            .close:hover {
                color: #e74c3c;
            }

            .modal-content form {
                margin-top: 20px;
                display: flex;
                flex-direction: column;
                gap: 15px;
            }

            .submit-btn {
                background: linear-gradient(90deg, #4db3a8, #3a8f87);
                color: #000;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;
                font-weight: 600;
            }

            .submit-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(77, 179, 168, 0.3);
            }

            .modal-content h2 {
                color: #000;
            }

            .modal-content p {
                color: #000;
            }

            .modal-content ol {
                color: #000;
            }

            .modal-content .note {
                color: #000;
            }

            footer {
                color: #000;
            }

            /* Graph Pagination Styles */
            .graph-footer {
                margin-top: 1.5rem;
                display: flex;
                justify-content: center;
            }

            .pagination {
                display: flex;
                gap: 1rem;
                align-items: center;
            }

            .page-btn {
                background: #f0f7ff;
                color: #2c3e50;
                border: 2px solid #4db3a8;
                border-radius: 8px;
                padding: 0.5rem 1.25rem;
                font-size: 0.9rem;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .page-btn i {
                font-size: 1rem;
            }

            .page-btn:hover {
                background: #e3f2fd;
                transform: translateY(-2px);
            }

            .page-btn.active {
                background: #4db3a8;
                color: #fff;
            }

            .counter {
                transition: all 0.3s ease;
            }

            .animated-text {
                opacity: 0;
                transform: translateY(20px);
            }

            .stat-card {
                overflow: hidden;
                position: relative;
            }

            .stat-card::after {
                content: "";
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                animation: shimmer 2s infinite;
            }

            @keyframes shimmer {
                100% {
                    left: 100%;
                }
            }

            /* Pie Chart Animations and Styling */
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.03); }
                100% { transform: scale(1); }
            }

            @keyframes colorPulse {
                0% { color: #4db3a8; }
                50% { color: #3a8f87; text-shadow: 0 0 8px rgba(58, 143, 135, 0.4); }
                100% { color: #4db3a8; }
            }

            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
            }

            @keyframes countUp {
                from { opacity: 0.5; }
                to { opacity: 1; }
            }

            @keyframes rotate {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            @keyframes pieSegmentPop {
                0% { transform: scale(1); }
                50% { transform: scale(1.08); }
                100% { transform: scale(1); }
            }

            .total-stats {
                transition: all 0.3s ease-out;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
                position: relative;
                overflow: hidden;
            }

            .total-stats:hover {
                box-shadow: 0 5px 15px rgba(77, 179, 168, 0.2);
                transform: translateY(-3px);
            }

            .total-stats::after {
                content: '';
                position: absolute;
                top: -50%;
                left: -50%;
                width: 200%;
                height: 200%;
                background: radial-gradient(circle, rgba(77, 179, 168, 0.1) 0%, rgba(255, 255, 255, 0) 70%);
                opacity: 0;
                transition: opacity 0.5s ease;
            }

            .total-stats:hover::after {
                opacity: 1;
                animation: rotate 8s linear infinite;
            }

            #totalEncoded {
                transition: all 0.3s ease;
                display: inline-block;
                position: relative;
            }

            .total-stats:hover #totalEncoded {
                animation: colorPulse 2s infinite;
            }

            #userStats > div {
                transition: all 0.3s ease;
                cursor: pointer;
                border-left: 3px solid transparent;
                position: relative;
                overflow: hidden;
            }

            #userStats > div:hover {
                background: #f1f7f7;
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
                transform: translateX(5px);
                border-left: 3px solid #4db3a8;
            }

            #userStats > div:hover span:last-child {
                animation: colorPulse 2s infinite;
            }

            #userStats > div::after {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(77, 179, 168, 0.1), transparent);
                transition: all 0.5s ease;
            }

            #userStats > div:hover::after {
                left: 100%;
                transition: all 0.5s ease;
            }

            #pieChart {
                transition: all 0.4s ease;
                filter: drop-shadow(0px 4px 6px rgba(0, 0, 0, 0.1));
            }

            #pieChartContainer .graph-wrapper {
                overflow: hidden;
                perspective: 1000px;
            }

            /* Chart Controls Styling */
            .chart-controls {
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            /* Custom Legend */
            .custom-legend {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
                position: absolute;
                top: 10px;
                right: 10px;
                background: rgba(255, 255, 255, 0.9);
                padding: 0.75rem;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                max-height: 300px;
                overflow-y: auto;
                opacity: 0;
                transform: translateX(10px);
                transition: all 0.3s ease;
            }

            .legend-item {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .legend-item:hover {
                background: #f1f7f7;
                transform: translateX(2px);
            }

            .legend-color {
                width: 12px;
                height: 12px;
                border-radius: 2px;
            }

            .legend-text {
                font-size: 0.85rem;
                color: #2c3e50;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 120px;
            }

            /* Enhanced tooltip */
            .enhanced-tooltip {
                position: absolute;
                background: white;
                border-radius: 8px;
                padding: 1rem;
                box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                pointer-events: none;
                opacity: 0;
                transition: opacity 0.2s ease;
                z-index: 1000;
                max-width: 250px;
            }

            .tooltip-header {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                margin-bottom: 0.5rem;
                padding-bottom: 0.5rem;
                border-bottom: 1px solid #eee;
            }

            .tooltip-color {
                width: 12px;
                height: 12px;
                border-radius: 2px;
            }

            .tooltip-title {
                font-weight: 600;
                color: #2c3e50;
            }

            .tooltip-body {
                display: flex;
                flex-direction: column;
                gap: 0.25rem;
            }

            .tooltip-value {
                font-size: 1.2rem;
                font-weight: 600;
                color: #4db3a8;
            }

            .tooltip-percentage {
                font-size: 0.9rem;
                color: #7f8c8d;
            }

            /* Message Animation */
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            
            /* Clickable Close Button for Messages */
            .message {
                position: relative;
            }
            
            .message .close-message {
                position: absolute;
                right: 10px;
                top: 10px;
                cursor: pointer;
                color: #666;
                transition: color 0.2s;
            }
            
            .message .close-message:hover {
                color: #333;
            }

            /* Updated button style for import, export, and undo */
            .data-btn {
                background: linear-gradient(90deg, #4db3a8, #3a8f87);
                color: white;
                border: none;
                padding: 15px 0;
                border-radius: 8px;
                cursor: pointer;
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 8px;
                font-size: 1rem;
                transition: all 0.3s ease;
                font-weight: 600;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                width: 100%;
                justify-content: center;
                text-align: center;
                word-wrap: break-word;
                white-space: normal;
                height: 75px;
            }
            
            .data-btn i {
                font-size: 1.3rem;
                margin-bottom: 3px;
            }
            
            .data-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(77, 179, 168, 0.4);
            }
            
            .data-btn.import-btn {
                background: linear-gradient(90deg, #4db3a8, #3a8f87);
            }
            
            .data-btn.export-btn {
                background: linear-gradient(90deg, #4db3a8, #3a8f87);
            }
            
            .data-btn.undo-btn {
                background: linear-gradient(90deg, #e74c3c, #c0392b);
            }
            
            .data-btn.undo-btn:hover {
                box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
            }
            
            .data-btn.delete-btn {
                background: linear-gradient(90deg, #e74c3c, #c0392b);
            }
            
            .data-btn.delete-btn:hover {
                box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
            }
            
            .data-buttons-container {
                display: flex;
                justify-content: space-between;
                gap: 8px;
                width: 100%;
                margin-top: auto;
            }
            
            .data-buttons-container .data-btn {
                flex: 1;
                min-width: 0;
                font-size: 0.85rem;
                height: 65px;
            }
        </style>
    </head>
    <body>
        <div class="dashboard">
            <div class="sidebar">
                <div class="sidebar-header">
                    <img src="./images/brgy_la_huerta.png" alt="Barangay La Huerta Logo">
                    <h2>Family Number Management System</h2>
                </div>
                <ul class="nav-menu">
                    <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super admin'): ?>
                        <li class="nav-item">
                            <a href="dashboard.php" class="nav-link active">
                                <i class="fas fa-home"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="family_registration.php" class="nav-link">
                                <i class="fas fa-user-plus"></i>
                                Register Family
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link dropdown-btn">
                                <i class="fas fa-folder"></i>
                                Records
                            </a>
                            <div class="dropdown-container">
                                <a href="view_family_records.php" class="dropdown-link">
                                    <i class="fas fa-table"></i>
                                    View Records
                                </a>
                                <a href="view_family_records.php?duplicates=1" class="dropdown-link">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Duplicate Records
                                </a>
                                <?php if ($_SESSION['role'] === 'super admin'): ?>
                                <a href="missing_family_numbers.php" class="dropdown-link">
                                    <i class="fas fa-list-ol"></i>
                                    Missing Family Numbers
                                </a>
                                <a href="recently_deleted.php" class="dropdown-link">
                                    <i class="fas fa-trash-alt"></i>
                                    Recently Deleted
                                </a>
                                <?php endif; ?>
                            </div>
                        </li>
                        <li class="nav-item">
                            <a href="scanner.php" class="nav-link">
                                <i class="fas fa-qrcode"></i>
                                Scan Family ID
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="manage_users.php" class="nav-link">
                                <i class="fas fa-users"></i>
                                Manage Users
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a href="dashboard.php" class="nav-link active">
                                <i class="fas fa-home"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="family_registration.php" class="nav-link">
                                <i class="fas fa-user-plus"></i>
                                Register Family
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link dropdown-btn">
                                <i class="fas fa-folder"></i>
                                Records
                            </a>
                            <div class="dropdown-container">
                                <a href="view_family_records.php" class="dropdown-link">
                                    <i class="fas fa-table"></i>
                                    View Records
                                </a>
                                <a href="view_family_records.php?duplicates=1" class="dropdown-link">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Duplicate Records
                                </a>
                                <?php if ($_SESSION['role'] === 'super admin'): ?>
                                <a href="missing_family_numbers.php" class="dropdown-link">
                                    <i class="fas fa-list-ol"></i>
                                    Missing Family Numbers
                                </a>
                                <a href="recently_deleted.php" class="dropdown-link">
                                    <i class="fas fa-trash-alt"></i>
                                    Recently Deleted
                                </a>
                                <?php endif; ?>
                            </div>
                        </li>
                        <li class="nav-item">
                            <a href="scanner.php" class="nav-link">
                                <i class="fas fa-qrcode"></i>
                                Scan Family ID
                            </a>
                        </li>
                    <?php endif; ?>
                    <li class="nav-item">
                        <a href="logout.php" class="nav-link">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </a>
                    </li>
                </ul>
            </div>

            <div class="main-content">
                <div class="header">
                    <div class="welcome-text">
                        <h1>Welcome, <?php
                            // Display full name if available, otherwise fall back to username
                            if(isset($_SESSION['fullname']) && !empty($_SESSION['fullname'])) {
                                echo htmlspecialchars(strtoupper($_SESSION['fullname']));
                            } else {
                                echo htmlspecialchars(strtoupper($_SESSION['username']));
                            }
                        ?>!</h1>
                        <p>Let's make today productive.</p>
                    </div>
                    <div class="date-time-container">
                        <i class="fas fa-clock"></i>
                        <span id="currentDateTime"></span>
                    </div>
                    <div class="user-info">
                        <div class="logo-container">
                            <img src="./images/himu-logo.png" alt="Health Information Management Unit Logo" title="Health Information Management Unit">
                        </div>
                        <div class="logo-container">
                            <img src="./images/bagong pilipinas.png" alt="Bagong Pilipinas" title="Bagong Pilipinas">
                        </div>
                        <div class="logo-container">
                            <img src="./images/alagang parañaque.png" alt="Alagang Parañaque" title="Alagang Parañaque">
                        </div>
                        <div class="logo-container">
                            <img src="./images/parañaque.png" alt="City of Parañaque" title="City of Parañaque">
                        </div>
                    </div>
                </div>
                
                <?php if(isset($_SESSION['success'])): ?>
                <div class="message success" style="background: #e7f7ef; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #4db3a8; display: flex; align-items: center; animation: fadeIn 0.5s ease-out;">
                    <i class="fas fa-check-circle" style="margin-right: 10px; font-size: 1.2rem; color: #4db3a8;"></i>
                    <div><?php echo nl2br(htmlspecialchars($_SESSION['success'])); ?></div>
                    <span class="close-message" onclick="this.parentNode.style.display='none'">&times;</span>
                </div>
                <?php unset($_SESSION['success']); endif; ?>
                
                <?php if(isset($_SESSION['warning'])): ?>
                <div class="message warning" style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #ffc107; display: flex; align-items: center; animation: fadeIn 0.5s ease-out;">
                    <i class="fas fa-exclamation-triangle" style="margin-right: 10px; font-size: 1.2rem; color: #ffc107;"></i>
                    <div><?php echo nl2br(htmlspecialchars($_SESSION['warning'])); ?></div>
                    <span class="close-message" onclick="this.parentNode.style.display='none'">&times;</span>
                </div>
                <?php unset($_SESSION['warning']); endif; ?>
                
                <?php if(isset($_SESSION['error'])): ?>
                <div class="message error" style="background: #fff5f5; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #e53e3e; display: flex; align-items: center; animation: fadeIn 0.5s ease-out;">
                    <i class="fas fa-times-circle" style="margin-right: 10px; font-size: 1.2rem; color: #e53e3e;"></i>
                    <div><?php echo nl2br(htmlspecialchars($_SESSION['error'])); ?></div>
                    <span class="close-message" onclick="this.parentNode.style.display='none'">&times;</span>
                </div>
                <?php unset($_SESSION['error']); endif; ?>

                <div class="stats-container">
                    <div class="stat-card">
                        <i class="fas fa-hospital"></i>
                        <?php
                        $sql = "SELECT COUNT(*) as total FROM family_registration";
                        $result = $conn->query($sql);
                        $total_families = $result->fetch_assoc()['total'];
                        ?>
                        <h3 class="counter" data-target="<?php echo $total_families; ?>">0</h3>
                        <p>Total Families</p>
                    </div>

                    <div class="stat-card">
                        <i class="fas fa-mars"></i>
                        <?php
                        $sql = "SELECT COUNT(*) as total FROM family_registration WHERE TRIM(male_name) != ''";
                        $result = $conn->query($sql);
                        $total_males = $result->fetch_assoc()['total'];
                        ?>
                        <h3 class="counter" data-target="<?php echo $total_males; ?>">0</h3>
                        <p>Total Male Names</p>
                    </div>

                    <div class="stat-card">
                        <i class="fas fa-venus"></i>
                        <?php
                        $sql = "SELECT COUNT(*) as total FROM family_registration WHERE TRIM(female_name) != ''";
                        $result = $conn->query($sql);
                        $total_females = $result->fetch_assoc()['total'];
                        ?>
                        <h3 class="counter" data-target="<?php echo $total_females; ?>">0</h3>
                        <p>Total Female Names</p>
                    </div>

                    <div class="stat-card">
                        <i class="fas fa-file-medical"></i>
                        <?php
                        $sql = "SELECT family_number FROM family_registration
                               ORDER BY
                               SUBSTRING_INDEX(family_number, '-', 1) DESC,
                               CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(family_number, '-', 2), '-', -1) AS UNSIGNED) DESC,
                               CAST(SUBSTRING_INDEX(family_number, '-', -1) AS UNSIGNED) DESC
                               LIMIT 1";
                        $result = $conn->query($sql);
                        $latest_number = $result->num_rows > 0 ? $result->fetch_assoc()['family_number'] : 'No entries';
                        ?>
                        <h3 class="latest-number animated-text"><?php echo htmlspecialchars($latest_number); ?></h3>
                        <p>Latest Family Number</p>
                    </div>

                    <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super admin'): ?>
                    <div class="stat-card">
                        <i class="fas fa-user-md"></i>
                        <?php
                        $sql = "SELECT COUNT(*) as total FROM users";
                        $result = $conn->query($sql);
                        $total_users = $result->fetch_assoc()['total'];
                        ?>
                        <h3 class="counter" data-target="<?php echo $total_users; ?>">0</h3>
                        <p>Total Users</p>
                    </div>

                    <div class="stat-card">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php
                        $sql = "SELECT
                            (SELECT COUNT(*) FROM (
                                SELECT family_number FROM family_registration
                                GROUP BY family_number HAVING COUNT(*) > 1
                            ) as dup_numbers) +
                            (SELECT COUNT(*) FROM (
                                SELECT male_name FROM family_registration
                                WHERE TRIM(male_name) != ''
                                GROUP BY male_name HAVING COUNT(*) > 1
                            ) as dup_males) +
                            (SELECT COUNT(*) FROM (
                                SELECT female_name FROM family_registration
                                WHERE TRIM(female_name) != ''
                                GROUP BY female_name HAVING COUNT(*) > 1
                            ) as dup_females) as duplicate_count";
                        $result = $conn->query($sql);
                        $duplicate_count = $result->fetch_assoc()['duplicate_count'];
                        ?>
                        <h3 class="counter" data-target="<?php echo $duplicate_count; ?>">0</h3>
                        <p>Duplicate Records</p>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="quick-actions">
                    <?php if ($_SESSION['role'] === 'staff'): ?>
                        <a href="family_registration.php" class="action-card">
                            <h3>Register Family</h3>
                            <p>Add a new family record to the system.</p>
                        </a>
                        <a href="view_family_records.php" class="action-card">
                            <h3>View Records</h3>
                            <p>View and manage family records.</p>
                        </a>
                        <a href="scanner.php" class="action-card">
                            <h3>Scan Family ID</h3>
                            <p>Validate family records using QR code scanner.</p>
                        </a>
                    <?php endif; ?>

                    <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super admin'): ?>
                        <a href="family_registration.php" class="action-card">
                            <h3>Register Family</h3>
                            <p>Add a new family record to the system.</p>
                        </a>
                        <a href="manage_users.php" class="action-card">
                            <h3>Manage Users</h3>
                            <p>Add, edit, or remove system users.</p>
                        </a>
                        <a href="scanner.php" class="action-card">
                            <h3>Scan Family ID</h3>
                            <p>Validate family records using QR code scanner.</p>
                        </a>
                        
                        <?php if ($_SESSION['role'] === 'super admin'): ?>
                        <div class="action-card wide-action-card">
                            <h3>Records Management</h3>
                            <p>View, manage, and track all family records</p>
                            <div class="data-buttons-container">
                                <a href="view_family_records.php" class="data-btn import-btn">
                                    <i class="fas fa-table"></i>
                                    View
                                </a>
                                <a href="view_family_records.php?duplicates=1" class="data-btn export-btn">
                                    <i class="fas fa-copy"></i>
                                    Duplicates
                                </a>
                                <a href="missing_family_numbers.php" class="data-btn undo-btn">
                                    <i class="fas fa-list-ol"></i>
                                    Missing
                                </a>
                                <a href="recently_deleted.php" class="data-btn delete-btn">
                                    <i class="fas fa-trash-alt"></i>
                                    Deleted
                                </a>
                            </div>
                        </div>
                        <?php else: ?>
                        <a href="view_family_records.php" class="action-card">
                            <h3>View Records</h3>
                            <p>View and manage family records.</p>
                        </a>
                        <a href="view_family_records.php?duplicates=1" class="action-card">
                            <h3><i class="fas fa-exclamation-triangle"></i> Duplicate Records</h3>
                            <p>View and resolve duplicate family numbers and names.</p>
                        </a>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super admin'): ?>
                        <?php if ($_SESSION['role'] === 'super admin'): ?>
                        <div class="action-card">
                            <h3>Data Management</h3>
                            <p>Import, export, or undo data changes</p>
                            <div class="data-buttons-container">
                                <button class="data-btn import-btn" onclick="document.getElementById('importModal').style.display='block'">
                                    <i class="fas fa-file-excel"></i>
                                    Import
                                </button>
                                <button class="data-btn export-btn" onclick="document.getElementById('exportModal').style.display='block'">
                                    <i class="fas fa-download"></i>
                                    Export
                                </button>
                                <button class="data-btn undo-btn" onclick="document.getElementById('undoImportModal').style.display='block'">
                                    <i class="fas fa-undo"></i>
                                    Undo
                                </button>
                            </div>
                        </div>
                        <?php elseif ($_SESSION['role'] === 'admin'): ?>
                        <div class="action-card">
                            <h3>Export Data</h3>
                            <p>Download all family records to Excel</p>
                            <button class="data-btn export-btn" onclick="document.getElementById('exportModal').style.display='block'">
                                <i class="fas fa-download"></i>
                                Export
                            </button>
                        </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>

                <div class="graph-container">
                    <div class="graph-header">
                        <h2 id="graphTitle">Daily Registrations by User</h2>
                        <select class="period-selector" id="periodSelector" onchange="updateGraph()">
                            <option value="day">Per Day</option>
                            <option value="week">Per Week</option>
                            <option value="month">Per Month</option>
                            <option value="year">Per Year</option>
                        </select>
                    </div>
                    <div class="graph-wrapper">
                        <canvas id="registrationsChart"></canvas>
                    </div>
                    <div class="graph-footer">
                        <div class="pagination">
                            <button class="page-btn active" data-graph="bar" style="background: #4db3a8; color: white; border: none;">
                                <i class="fas fa-chart-bar"></i> Bar Graph
                            </button>
                            <button class="page-btn" data-graph="pie" style="background: white; color: #4db3a8; border: 2px solid #4db3a8;">
                                <i class="fas fa-chart-pie"></i> Pie Chart
                            </button>
                        </div>
                    </div>
                </div>

                <!-- New Pie Chart -->
                <div class="graph-container" id="pieChartContainer" style="display: none;">
                    <div class="graph-header">
                        <h2>Registration Distribution by User</h2>
                        <div class="chart-controls">
                            <select class="period-selector" id="pieChartPeriod">
                                <option value="all">All Time</option>
                                <option value="year">This Year</option>
                                <option value="month">This Month</option>
                                <option value="week">This Week</option>
                            </select>
                        </div>
                    </div>
                    <div class="graph-wrapper" style="display: flex; align-items: center; gap: 2rem;">
                        <div class="pie-stats" style="flex: 1; padding: 1rem;">
                            <div class="total-stats" style="background: #f8f9fa; padding: 1.5rem; border-radius: 12px; margin-bottom: 1rem;">
                                <h3 style="color: #2c3e50; margin-bottom: 0.5rem;">Total Encoded</h3>
                                <p id="totalEncoded" style="font-size: 3rem; font-weight: bold; color: #4db3a8; margin: 0;">0</p>
                            </div>
                            <div class="user-stats" id="userStats" style="max-height: 300px; overflow-y: auto;">
                                <!-- User stats will be populated here -->
                            </div>
                        </div>
                        <div style="flex: 2; height: 380px; position: relative;">
                            <canvas id="pieChart"></canvas>
                            <div id="customLegend" class="custom-legend"></div>
                        </div>
                    </div>
                    <div class="graph-footer">
                        <div class="pagination">
                            <button class="page-btn" data-graph="bar" style="background: white; color: #4db3a8; border: 2px solid #4db3a8;">
                                <i class="fas fa-chart-bar"></i> Bar Graph
                            </button>
                            <button class="page-btn active" data-graph="pie" style="background: #4db3a8; color: white; border: none;">
                                <i class="fas fa-chart-pie"></i> Pie Chart
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Import Modal -->
        <div id="importModal" class="modal">
            <div class="modal-content">
                <span class="close" onclick="document.getElementById('importModal').style.display='none'">&times;</span>
                <h2>Import Family Records</h2>
                <p>Upload your CSV file containing family records.</p>
                <p>Required columns (in order):</p>
                <ol style="margin: 10px 0 20px 25px;">
                    <li>Registration Date (YYYY-MM-DD)</li>
                    <li>Family Number</li>
                    <li>Male Name</li>
                    <li>Female Name</li>
                    <li>Address</li>
                    <li>Remarks (optional)</li>
                    <li>Registered By (optional)</li>
                </ol>
                <form action="import_excel.php" method="post" enctype="multipart/form-data">
                    <div style="margin-bottom: 15px;">
                        <label for="excel_file" style="display: block; margin-bottom: 5px; font-weight: 500;">Select CSV File:</label>
                        <input type="file" name="excel_file" id="excel_file" accept=".csv" required>
                    </div>
                    <button type="submit" class="submit-btn">Upload</button>
                </form>
                <p style="margin-top: 15px; font-size: 0.9em; color: #666;">
                    Note: Save your Excel file as CSV (Comma delimited) before uploading.
                </p>
            </div>
        </div>

        <!-- Export Modal -->
        <div id="exportModal" class="modal">
            <div class="modal-content">
                <span class="close" onclick="document.getElementById('exportModal').style.display='none'">&times;</span>
                <h2>Export Family Records</h2>
                <p>The following data will be exported to Excel:</p>
                <ul style="margin: 10px 0 20px 25px;">
                    <li>Registration Date</li>
                    <li>Family Number</li>
                    <li>Male Name</li>
                    <li>Female Name</li>
                    <li>Address</li>
                    <li>Remarks</li>
                    <li>Registered By</li>
                </ul>
                <p style="margin-bottom: 15px; color: #666;">
                    <i class="fas fa-info-circle"></i> The file will be downloaded automatically.
                </p>
                <button onclick="window.location.href='export_excel.php'" class="submit-btn data-btn export-btn">
                    <i class="fas fa-download"></i>
                    Download
                </button>
            </div>
        </div>
        
        <!-- Undo Import Modal -->
        <div id="undoImportModal" class="modal">
            <div class="modal-content">
                <span class="close" onclick="document.getElementById('undoImportModal').style.display='none'">&times;</span>
                <h2>Undo Last Import</h2>
                <p>This action will revert the most recent batch of imported records.</p>
                <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #ffc107;">
                    <p style="margin: 0; color: #856404;">
                        <i class="fas fa-exclamation-triangle"></i> Warning: This action cannot be undone. All records from the last import will be permanently removed.
                    </p>
                </div>
                <button onclick="confirmUndoImport()" class="submit-btn data-btn undo-btn">
                    <i class="fas fa-undo"></i>
                    Confirm Undo
                </button>
            </div>
        </div>
    </body>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Function to confirm and execute undo import operation
        function confirmUndoImport() {
            if (confirm("Are you absolutely sure you want to undo the last import? This cannot be reversed.")) {
                // Redirect to the undo import script
                window.location.href = 'undo_last_import.php';
            }
        }
        
        // Global updateGraph function - moved outside DOMContentLoaded
        let chart = null;
        function updateGraph() {
            const period = document.getElementById('periodSelector').value;
            const graphTitle = document.getElementById('graphTitle');

            // Update the title based on period
            switch(period) {
                case 'day':
                    graphTitle.textContent = 'Daily Registrations by User';
                    break;
                case 'week':
                    graphTitle.textContent = 'Weekly Registrations by User';
                    break;
                case 'month':
                    graphTitle.textContent = 'Monthly Registrations by User';
                    break;
                case 'year':
                    graphTitle.textContent = 'Yearly Registrations by User';
                    break;
            }

            fetch(`get_registration_stats.php?period=${period}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Bar chart data received:', data);

                    // Check if we have actual data
                    if (!data.datasets || data.datasets.length === 0 ||
                        !data.datasets.some(ds => ds.data && ds.data.some(val => val > 0))) {
                        console.log('No registration data found for the selected period');

                        // Show a message in the chart area
                        if (chart) {
                            chart.destroy();
                        }

                        const chartCanvas = document.getElementById('registrationsChart');
                        const ctx = chartCanvas.getContext('2d');
                        ctx.clearRect(0, 0, chartCanvas.width, chartCanvas.height);
                        ctx.font = '14px Arial';
                        ctx.textAlign = 'center';
                        ctx.fillStyle = '#666';
                        ctx.fillText('No registration data available for this period', chartCanvas.width/2, chartCanvas.height/2);

                        return;
                    }

                    if (chart) {
                        chart.destroy();
                    }

                    const ctx = document.getElementById('registrationsChart').getContext('2d');
                    chart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: data.labels,
                            datasets: data.datasets
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        stepSize: 1
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    position: 'top',
                                    labels: {
                                        padding: 20,
                                        font: {
                                            size: 12
                                        }
                                    }
                                },
                                tooltip: {
                                    mode: 'index',
                                    intersect: false,
                                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                                    titleColor: '#2c3e50',
                                    bodyColor: '#2c3e50',
                                    borderColor: '#e0e0e0',
                                    borderWidth: 1,
                                    padding: 10,
                                    displayColors: true,
                                    callbacks: {
                                        label: function(context) {
                                            let label = context.dataset.label || '';
                                            if (label) {
                                                label += ': ';
                                            }
                                            label += context.parsed.y;
                                            return label;
                                        }
                                    }
                                }
                            },
                            interaction: {
                                mode: 'nearest',
                                axis: 'x',
                                intersect: false
                            },
                            animation: {
                                duration: 500,
                                easing: 'easeInOutQuart'
                            }
                        }
                    });
                })
                .catch(error => console.error('Error:', error));
        }
        
        // Function to switch between graphs - defined in global scope
        function showGraph(graphType) {
            // Update button states
            document.querySelectorAll('.page-btn').forEach(btn => {
                if (btn.dataset.graph === graphType) {
                    btn.classList.add('active');
                    btn.style.background = '#4db3a8';
                    btn.style.color = 'white';
                    btn.style.border = 'none';
                } else {
                    btn.classList.remove('active');
                    btn.style.background = 'white';
                    btn.style.color = '#4db3a8';
                    btn.style.border = '2px solid #4db3a8';
                }
            });

            // Get references to the containers
            const barChartContainer = document.querySelector('.graph-container:not(#pieChartContainer)');
            const pieChartContainer = document.getElementById('pieChartContainer');

            // Show/hide appropriate graph containers with enhanced 3D animation
            if (graphType === 'bar') {
                // Animate pie chart out with 3D effect
                pieChartContainer.style.opacity = '1';
                pieChartContainer.style.transform = 'scale(1) perspective(1000px) rotateY(0deg)';
                pieChartContainer.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';

                setTimeout(() => {
                    pieChartContainer.style.opacity = '0';
                    pieChartContainer.style.transform = 'scale(0.95) perspective(1000px) rotateY(-20deg)';

                    setTimeout(() => {
                        // Hide pie chart
                        pieChartContainer.style.display = 'none';

                        // Show and animate bar chart in with 3D effect
                        barChartContainer.style.display = 'block';
                        barChartContainer.style.opacity = '0';
                        barChartContainer.style.transform = 'scale(0.95) perspective(1000px) rotateY(20deg)';
                        barChartContainer.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';

                        // Force reflow
                        barChartContainer.offsetHeight;

                        setTimeout(() => {
                            barChartContainer.style.opacity = '1';
                            barChartContainer.style.transform = 'scale(1) perspective(1000px) rotateY(0deg)';

                            // Add a subtle shadow effect
                            barChartContainer.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
                            setTimeout(() => {
                                barChartContainer.style.boxShadow = 'none';
                            }, 600);
                        }, 50);
                    }, 400);
                }, 50);
            } else {
                // Animate bar chart out with 3D effect
                barChartContainer.style.opacity = '1';
                barChartContainer.style.transform = 'scale(1) perspective(1000px) rotateY(0deg)';
                barChartContainer.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';

                setTimeout(() => {
                    barChartContainer.style.opacity = '0';
                    barChartContainer.style.transform = 'scale(0.95) perspective(1000px) rotateY(20deg)';

                    setTimeout(() => {
                        // Hide bar chart
                        barChartContainer.style.display = 'none';

                        // Show and animate pie chart in with 3D effect
                        pieChartContainer.style.display = 'block';
                        pieChartContainer.style.opacity = '0';
                        pieChartContainer.style.transform = 'scale(0.95) perspective(1000px) rotateY(-20deg)';
                        pieChartContainer.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out';

                        // Force reflow
                        pieChartContainer.offsetHeight;

                        setTimeout(() => {
                            pieChartContainer.style.opacity = '1';
                            pieChartContainer.style.transform = 'scale(1) perspective(1000px) rotateY(0deg)';

                            // Add a subtle shadow effect
                            pieChartContainer.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
                            setTimeout(() => {
                                pieChartContainer.style.boxShadow = 'none';
                            }, 600);

                            // Re-animate pie chart segments
                            if (pieChart) {
                                // Give the chart time to initialize before animating
                                setTimeout(() => {
                                    // Check if pieChart is still valid after timeout
                                    if (pieChart && pieChart.canvas) {
                                        try {
                                            animatePieSegments(pieChart);
                                        } catch (err) {
                                            console.error('Error animating pie segments:', err);
                                        }
                                    }
                                }, 300);
                            }
                        }, 50);
                    }, 400);
                }, 50);
            }
        }

        // Function to animate pie segments sequentially - moved to global scope
        function animatePieSegments(chart) {
            // Get the canvas element
            if (!chart || !chart.canvas) {
                console.error('Invalid chart or canvas in animatePieSegments');
                return; // Exit the function if chart or canvas is not valid
            }
            
            const canvas = chart.canvas;

            // Add a nice 3D reveal effect when switching to the pie chart
            if (canvas) {
                canvas.style.opacity = 0;
                canvas.style.transform = 'scale(0.8) rotateY(-15deg)';
                canvas.style.transition = 'opacity 0.7s ease-out, transform 0.7s ease-out';

                setTimeout(() => {
                    canvas.style.opacity = 1;
                    canvas.style.transform = 'scale(1) rotateY(0deg)';
                }, 100);
            }

            // Animate the total count with a counting effect
            const totalElement = document.getElementById('totalEncoded');
            if (!totalElement) {
                console.error('totalEncoded element not found');
                return; // Exit if the element doesn't exist
            }
            
            const targetTotal = parseInt(totalElement.textContent);

            if (totalElement && !isNaN(targetTotal)) {
                let startValue = 0;
                const duration = 1500; // Slightly longer for more dramatic effect
                const frameDuration = 1000/60;
                const totalFrames = Math.round(duration / frameDuration);
                let currentFrame = 0;

                // Add a subtle glow effect to the total count container
                const totalStatsContainer = document.querySelector('.total-stats');
                if (totalStatsContainer) {
                    totalStatsContainer.style.boxShadow = '0 0 15px rgba(77, 179, 168, 0.2)';
                    setTimeout(() => {
                        totalStatsContainer.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.05)';
                    }, duration + 200);
                }

                totalElement.textContent = '0';
                totalElement.style.textShadow = '0 0 5px rgba(77, 179, 168, 0.3)';

                const counter = setInterval(() => {
                    currentFrame++;
                    const progress = currentFrame / totalFrames;
                    const currentCount = Math.round(easeOutQuad(progress) * targetTotal);

                    totalElement.textContent = currentCount;

                    if (currentFrame === totalFrames) {
                        clearInterval(counter);
                        totalElement.textContent = targetTotal;

                        // Add a subtle flash effect when counting completes
                        totalElement.style.textShadow = '0 0 10px rgba(77, 179, 168, 0.5)';
                        setTimeout(() => {
                            totalElement.style.textShadow = '0 0 0px rgba(77, 179, 168, 0)';
                        }, 300);
                    }
                }, frameDuration);
            }

            // Animate user stats entries with a staggered effect
            const userStats = document.querySelectorAll('#userStats > div');
            if (userStats && userStats.length > 0) {
                userStats.forEach((stat, index) => {
                    stat.style.opacity = 0;
                    stat.style.transform = 'translateY(20px) translateX(-10px)';
                    stat.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';

                    setTimeout(() => {
                        stat.style.opacity = 1;
                        stat.style.transform = 'translateY(0) translateX(0)';

                        // Add a subtle flash effect to the percentage
                        const percentElement = stat.querySelector('span:last-child');
                        if (percentElement) {
                            percentElement.style.textShadow = '0 0 8px rgba(77, 179, 168, 0.4)';
                            setTimeout(() => {
                                percentElement.style.textShadow = 'none';
                            }, 500);
                        }
                    }, 300 + (index * 120));
                });
            }

            // Add event listeners for segment hover effects
            if (canvas && chart) {
                try {
                    // First remove any existing event listeners to prevent duplicates
                    canvas.removeEventListener('mousemove', handleMouseMove);
                    
                    // Define the handler function separately so we can remove it if needed
                    function handleMouseMove(e) {
                        try {
                            if (!chart || !chart.ctx || !chart.canvas) return;
                            
                            // Use try/catch to prevent errors from crashing the dashboard
                            const activeElements = chart.getElementsAtEventForMode(
                                e, 
                                'nearest', 
                                { intersect: true }, 
                                false
                            );

                            if (userStats && userStats.length) {
                                if (activeElements && activeElements.length > 0) {
                                    const segment = activeElements[0];

                                    // Highlight the corresponding user stat
                                    if (segment.index < userStats.length) {
                                        userStats.forEach((stat, i) => {
                                            if (i === segment.index) {
                                                stat.style.transform = 'translateX(8px)';
                                                stat.style.borderLeft = '3px solid #4db3a8';
                                                stat.style.background = '#f1f7f7';
                                            } else {
                                                stat.style.transform = 'translateX(0)';
                                                stat.style.borderLeft = '3px solid transparent';
                                                stat.style.background = '#f8f9fa';
                                            }
                                        });
                                    }
                                } else {
                                    // Reset all stats
                                    userStats.forEach(stat => {
                                        stat.style.transform = 'translateX(0)';
                                        stat.style.borderLeft = '3px solid transparent';
                                        stat.style.background = '#f8f9fa';
                                    });
                                }
                            }
                        } catch (err) {
                            console.error('Error in chart mousemove handler:', err);
                        }
                    }
                    
                    // Add the event listener
                    canvas.addEventListener('mousemove', handleMouseMove);
                } catch (err) {
                    console.error('Error setting up chart event listeners:', err);
                }
            }
        }
        
        // Easing function for smoother animation - moved to global scope
        function easeOutQuad(x) {
            return 1 - (1 - x) * (1 - x);
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Initial chart load
            updateGraph();

            // Period change handler
            document.getElementById('periodSelector').addEventListener('change', updateGraph);

            // Load and render the pie chart
            const pieCtx = document.getElementById('pieChart').getContext('2d');
            let pieChart = null;

            // Create enhanced tooltip element
            const enhancedTooltip = document.createElement('div');
            enhancedTooltip.className = 'enhanced-tooltip';
            enhancedTooltip.style.display = 'none';
            document.body.appendChild(enhancedTooltip);

            // Function to create gradient backgrounds for pie chart
            function createGradients(ctx, data) {
                const gradients = [];
                const baseColors = [
                    '#4db3a8', '#3498db', '#e74c3c', '#f1c40f', '#9b59b6',
                    '#1abc9c', '#e67e22', '#34495e', '#7f8c8d', '#16a085'
                ];

                data.forEach((_, index) => {
                    const colorIndex = index % baseColors.length;
                    const baseColor = baseColors[colorIndex];

                    // Create a lighter version of the base color
                    const lighterColor = getLighterColor(baseColor, 0.3);

                    // Create gradient
                    const gradient = ctx.createLinearGradient(0, 0, 0, 400);
                    gradient.addColorStop(0, baseColor);
                    gradient.addColorStop(1, lighterColor);

                    gradients.push(gradient);
                });

                return gradients;
            }

            // Function to get a lighter version of a color
            function getLighterColor(hex, factor) {
                // Convert hex to RGB
                let r = parseInt(hex.slice(1, 3), 16);
                let g = parseInt(hex.slice(3, 5), 16);
                let b = parseInt(hex.slice(5, 7), 16);

                // Make it lighter
                r = Math.min(255, Math.round(r + (255 - r) * factor));
                g = Math.min(255, Math.round(g + (255 - g) * factor));
                b = Math.min(255, Math.round(b + (255 - b) * factor));

                // Convert back to hex
                return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
            }

            // Function to create custom legend
            function createCustomLegend(chart) {
                const legendContainer = document.getElementById('customLegend');
                legendContainer.innerHTML = '';

                // Get chart data
                const data = chart.data;

                // Create legend items
                data.labels.forEach((label, index) => {
                    const legendItem = document.createElement('div');
                    legendItem.className = 'legend-item';
                    legendItem.dataset.index = index;

                    const color = document.createElement('div');
                    color.className = 'legend-color';
                    color.style.backgroundColor = data.datasets[0].backgroundColor[index];

                    const text = document.createElement('div');
                    text.className = 'legend-text';
                    text.textContent = label;

                    legendItem.appendChild(color);
                    legendItem.appendChild(text);
                    legendContainer.appendChild(legendItem);

                    // Add event listeners
                    legendItem.addEventListener('mouseenter', () => {
                        // Highlight corresponding segment
                        chart.setActiveElements([{
                            datasetIndex: 0,
                            index: index
                        }]);
                        chart.update();

                        // Highlight this legend item
                        legendItem.style.background = '#f1f7f7';
                        legendItem.style.transform = 'translateX(2px)';
                    });

                    legendItem.addEventListener('mouseleave', () => {
                        // Remove highlighting
                        chart.setActiveElements([]);
                        chart.update();

                        // Reset this legend item
                        legendItem.style.background = 'transparent';
                        legendItem.style.transform = 'translateX(0)';
                    });

                    legendItem.addEventListener('click', () => {
                        // Toggle visibility of the segment
                        const meta = chart.getDatasetMeta(0);
                        const arc = meta.data[index];

                        meta.data[index].hidden = !meta.data[index].hidden;
                        chart.update();

                        // Update legend item appearance
                        if (meta.data[index].hidden) {
                            legendItem.style.opacity = 0.5;
                        } else {
                            legendItem.style.opacity = 1;
                        }
                    });
                });

                // Show the legend with animation
                setTimeout(() => {
                    legendContainer.style.opacity = 1;
                    legendContainer.style.transform = 'translateX(0)';
                }, 500);
            }

            // Function to load pie chart data with period filter
            function loadPieChart(period = 'all') {
                // Show loading state
                const chartCanvas = document.getElementById('pieChart');
                const ctx = chartCanvas.getContext('2d');
                ctx.clearRect(0, 0, chartCanvas.width, chartCanvas.height);
                ctx.font = '14px Arial';
                ctx.textAlign = 'center';
                ctx.fillStyle = '#666';
                ctx.fillText('Loading chart data...', chartCanvas.width/2, chartCanvas.height/2);

                fetch(`get_pie_chart_data.php${period !== 'all' ? `?period=${period}` : ''}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Pie chart data received:', data);

                        // Check if we have actual data
                        if (!data.datasets || data.datasets.length === 0 ||
                            !data.datasets[0].data || data.datasets[0].data.length === 0) {
                            console.log('No registration distribution data found');

                            // Show a message in the chart area
                            if (pieChart) {
                                pieChart.destroy();
                            }

                            const chartCanvas = document.getElementById('pieChart');
                            const ctx = chartCanvas.getContext('2d');
                            ctx.clearRect(0, 0, chartCanvas.width, chartCanvas.height);
                            ctx.font = '14px Arial';
                            ctx.textAlign = 'center';
                            ctx.fillStyle = '#666';
                            ctx.fillText('No registration data available', chartCanvas.width/2, chartCanvas.height/2);

                            document.getElementById('totalEncoded').textContent = '0';
                            document.getElementById('userStats').innerHTML =
                                '<div style="text-align: center; padding: 1rem; color: #666;">No data available</div>';

                            // Hide custom legend
                            document.getElementById('customLegend').style.opacity = 0;

                            return;
                        }

                        if (pieChart) {
                            pieChart.destroy();
                        }

                        // Calculate total
                        const total = data.datasets[0].data.reduce((a, b) => a + b, 0);
                        document.getElementById('totalEncoded').textContent = total;

                        // Update user stats
                        const userStatsContainer = document.getElementById('userStats');
                        userStatsContainer.innerHTML = '';

                        // Create gradients for the chart
                        const gradients = createGradients(pieCtx, data.datasets[0].data);

                        // Update the dataset with gradients
                        data.datasets[0].backgroundColor = gradients;

                        // Create user stats with color indicators
                        data.labels.forEach((label, index) => {
                            const count = data.datasets[0].data[index];
                            const percentage = ((count / total) * 100).toFixed(1);
                            const bgColor = data.datasets[0].backgroundColor[index];

                            const statDiv = document.createElement('div');
                            statDiv.style.background = '#f8f9fa';
                            statDiv.style.padding = '1rem';
                            statDiv.style.borderRadius = '8px';
                            statDiv.style.marginBottom = '0.5rem';
                            statDiv.dataset.index = index;

                            statDiv.innerHTML = `
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                                        <div style="width: 10px; height: 10px; border-radius: 2px; background: ${typeof bgColor === 'object' ? bgColor.toString() : bgColor}"></div>
                                        <span style="color: #2c3e50; font-weight: 500;">${label}</span>
                                    </div>
                                    <span style="color: #4db3a8; font-weight: 600;">${count} (${percentage}%)</span>
                                </div>
                            `;

                            userStatsContainer.appendChild(statDiv);

                            // Add hover effect to highlight corresponding pie segment
                            statDiv.addEventListener('mouseenter', () => {
                                if (pieChart) {
                                    pieChart.setActiveElements([{
                                        datasetIndex: 0,
                                        index: index
                                    }]);
                                    pieChart.update();

                                    // Highlight this stat
                                    statDiv.style.transform = 'translateX(8px)';
                                    statDiv.style.borderLeft = '3px solid #4db3a8';
                                    statDiv.style.background = '#f1f7f7';
                                }
                            });

                            statDiv.addEventListener('mouseleave', () => {
                                if (pieChart) {
                                    pieChart.setActiveElements([]);
                                    pieChart.update();

                                    // Reset this stat
                                    statDiv.style.transform = 'translateX(0)';
                                    statDiv.style.borderLeft = '3px solid transparent';
                                    statDiv.style.background = '#f8f9fa';
                                }
                            });
                        });

                        // Create the chart
                        pieChart = new Chart(pieCtx, {
                            type: 'pie',
                            data: {
                                labels: data.labels,
                                datasets: data.datasets
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        display: false // We're using custom legend
                                    },
                                    tooltip: {
                                        enabled: false, // We're using custom tooltip
                                    }
                                },
                                animation: {
                                    animateScale: true,
                                    animateRotate: true,
                                    duration: 1200,
                                    easing: 'easeOutElastic',
                                    delay: (context) => {
                                        // Add a sequential delay to each segment
                                        return context.dataIndex * 100;
                                    }
                                },
                                elements: {
                                    arc: {
                                        borderWidth: 2,
                                        borderColor: '#fff',
                                        hoverBorderColor: '#fff',
                                        hoverBorderWidth: 3,
                                        hoverOffset: 10
                                    }
                                },
                                onHover: (event, activeElements) => {
                                    // Add pulse animation on hover
                                    if (activeElements && activeElements.length > 0) {
                                        event.native.target.style.cursor = 'pointer';

                                        // Apply a pop animation to the hovered segment
                                        const segment = activeElements[0];
                                        const meta = pieChart.getDatasetMeta(segment.datasetIndex);
                                        const arc = meta.data[segment.index];

                                        // Only animate if we're not already animating
                                        if (!arc._animating) {
                                            arc._animating = true;

                                            // Store original values
                                            const originalOffset = arc.options.offset || 0;

                                            // Apply animation
                                            arc.options.offset = 8;
                                            pieChart.update('none');

                                            // Reset after animation
                                            setTimeout(() => {
                                                arc.options.offset = originalOffset;
                                                pieChart.update('none');
                                                arc._animating = false;
                                            }, 300);
                                        }

                                        // Show enhanced tooltip
                                        const index = segment.index;
                                        const label = data.labels[index];
                                        const value = data.datasets[0].data[index];
                                        const percentage = ((value / total) * 100).toFixed(1);
                                        const bgColor = data.datasets[0].backgroundColor[index];

                                        enhancedTooltip.innerHTML = `
                                            <div class="tooltip-header">
                                                <div class="tooltip-color" style="background: ${typeof bgColor === 'object' ? bgColor.toString() : bgColor}"></div>
                                                <div class="tooltip-title">${label}</div>
                                            </div>
                                            <div class="tooltip-body">
                                                <div class="tooltip-value">${value} registrations</div>
                                                <div class="tooltip-percentage">${percentage}% of total</div>
                                            </div>
                                        `;

                                        enhancedTooltip.style.display = 'block';
                                        enhancedTooltip.style.opacity = '1';

                                        // Position the tooltip near the cursor
                                        const rect = event.native.target.getBoundingClientRect();
                                        const x = event.native.clientX;
                                        const y = event.native.clientY;

                                        enhancedTooltip.style.left = `${x + 15}px`;
                                        enhancedTooltip.style.top = `${y - 15}px`;

                                        // Highlight corresponding user stat
                                        const userStats = document.querySelectorAll('#userStats > div');
                                        userStats.forEach((stat, i) => {
                                            if (i === index) {
                                                stat.style.transform = 'translateX(8px)';
                                                stat.style.borderLeft = '3px solid #4db3a8';
                                                stat.style.background = '#f1f7f7';
                                            } else {
                                                stat.style.transform = 'translateX(0)';
                                                stat.style.borderLeft = '3px solid transparent';
                                                stat.style.background = '#f8f9fa';
                                            }
                                        });
                                    } else {
                                        event.native.target.style.cursor = 'default';

                                        // Hide tooltip
                                        enhancedTooltip.style.opacity = '0';
                                        setTimeout(() => {
                                            enhancedTooltip.style.display = 'none';
                                        }, 200);

                                        // Reset all user stats
                                        const userStats = document.querySelectorAll('#userStats > div');
                                        userStats.forEach(stat => {
                                            stat.style.transform = 'translateX(0)';
                                            stat.style.borderLeft = '3px solid transparent';
                                            stat.style.background = '#f8f9fa';
                                        });
                                    }
                                }
                            }
                        });

                        // Create custom legend
                        createCustomLegend(pieChart);

                        // Add a sequential animation for each segment in the pie chart
                        animatePieSegments(pieChart);
                    })
                    .catch(error => console.error('Error loading pie chart:', error));
            }

            // Initial pie chart load
            loadPieChart();

            // Initialize with bar graph visible
            showGraph('bar');

            // Add event listeners for pagination buttons
            document.querySelectorAll('.page-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    showGraph(this.dataset.graph);
                });
            });

            // Add event listener for period selector
            document.getElementById('pieChartPeriod').addEventListener('change', function() {
                loadPieChart(this.value);
            });

            // Add counter animation for statistics
            const counters = document.querySelectorAll('.counter');
            const speed = 200; // The lower the faster

            // Animate numeric counters
            counters.forEach(counter => {
                const animate = () => {
                    const target = +counter.getAttribute('data-target');
                    const count = +counter.innerText;
                    const increment = Math.ceil(target / speed);

                    if (count < target) {
                        counter.innerText = count + increment;
                        setTimeout(animate, 1);
                    } else {
                        counter.innerText = target;
                    }
                };
                animate();
            });

            // Initialize dropdown functionality for sidebar
            const dropdownBtns = document.querySelectorAll('.dropdown-btn');
            
            dropdownBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    this.classList.toggle('active');
                    const dropdownContent = this.nextElementSibling;
                    dropdownContent.classList.toggle('show');
                });
            });

            // Animate latest family number
            const latestNumber = document.querySelector('.animated-text');
            if (latestNumber) {
                latestNumber.style.opacity = 0;
                setTimeout(() => {
                    latestNumber.style.transition = 'opacity 1s ease-in-out, transform 1s ease-in-out';
                    latestNumber.style.transform = 'translateY(0)';
                    latestNumber.style.opacity = 1;
                }, 300);
            }
        });

        // Date and Time updater
        function updateDateTime() {
            const now = new Date();
            const weekday = now.toLocaleDateString('en-US', { weekday: 'long' });
            const dateTimeOptions = { year: 'numeric', month: 'long', day: 'numeric' };
            const dateStr = now.toLocaleDateString('en-US', dateTimeOptions);
            let hours = now.getHours();
            const minutes = now.getMinutes().toString().padStart(2, '0');
            const seconds = now.getSeconds().toString().padStart(2, '0');
            const ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12;
            hours = hours ? hours : 12; // the hour '0' should be '12'
            const timeStr = `${hours.toString().padStart(2, '0')}:${minutes}:${seconds} ${ampm}`;
            
            document.getElementById('currentDateTime').innerHTML = `<div style="font-weight: bold; margin-bottom: 3px;">${weekday}</div>${dateStr} | ${timeStr}`;
        }
        setInterval(updateDateTime, 1000);
        updateDateTime();
    </script>
    </html>
    <?php $conn->close(); ?>