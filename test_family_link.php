<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: login.php");
    exit();
}

include "db.php";

echo "<h2>Test Family Link for FN-25-1742</h2>";

// Find the record for FN-25-1742
$sql = "SELECT * FROM family_registration WHERE family_number = 'FN-25-1742'";
$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    $row = $result->fetch_assoc();
    echo "<h3>Record Found:</h3>";
    echo "<p><strong>ID:</strong> " . $row['id'] . "</p>";
    echo "<p><strong>Family Number:</strong> " . htmlspecialchars($row['family_number']) . "</p>";
    echo "<p><strong>Male Name:</strong> " . htmlspecialchars($row['male_name']) . "</p>";
    echo "<p><strong>Female Name:</strong> " . htmlspecialchars($row['female_name']) . "</p>";
    
    echo "<h3>Test Links:</h3>";
    echo "<p><a href='view_family_detail.php?id=" . $row['id'] . "' target='_blank'>Test Detail Link (ID: " . $row['id'] . ")</a></p>";
    echo "<p><a href='debug_family_detail.php?id=" . $row['id'] . "' target='_blank'>Debug Detail Link (ID: " . $row['id'] . ")</a></p>";
    
    echo "<h3>Simulated Table Row:</h3>";
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr>";
    echo "<td>" . date('M d, Y', strtotime($row['registration_date'])) . "</td>";
    echo "<td class='family-number'>";
    echo "<div class='family-number-container'>";
    echo "<a href='view_family_detail.php?id=" . $row['id'] . "' class='family-number-link'>";
    echo "<span>" . htmlspecialchars($row['family_number']) . "</span>";
    echo "</a>";
    echo "<button class='copy-btn' onclick=\"copyFamilyNumber('" . htmlspecialchars($row['family_number']) . "', event)\" title='Copy family number'>";
    echo "<i class='fas fa-copy'></i>";
    echo "</button>";
    echo "</div>";
    echo "</td>";
    echo "<td>" . htmlspecialchars($row['male_name']) . "</td>";
    echo "</tr>";
    echo "</table>";
    echo "</div>";
    
} else {
    echo "<p style='color: red;'>No record found for family number FN-25-1742</p>";
    
    // Show some existing records
    echo "<h3>Available Records (last 5):</h3>";
    $sql2 = "SELECT id, family_number, male_name, female_name FROM family_registration ORDER BY id DESC LIMIT 5";
    $result2 = $conn->query($sql2);
    
    if ($result2 && $result2->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Family Number</th><th>Male Name</th><th>Female Name</th><th>Test Link</th></tr>";
        while ($row2 = $result2->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row2['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row2['family_number']) . "</td>";
            echo "<td>" . htmlspecialchars($row2['male_name']) . "</td>";
            echo "<td>" . htmlspecialchars($row2['female_name']) . "</td>";
            echo "<td><a href='view_family_detail.php?id=" . $row2['id'] . "' target='_blank'>Test</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
}

$conn->close();
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Family Link</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h2, h3 { color: #333; }
        table { margin: 10px 0; }
        th, td { padding: 8px; text-align: left; }
        th { background: #f0f0f0; }
        
        /* Copy the same CSS from view_family_records.php */
        .family-number {
            font-weight: 600;
            font-size: 14px;
        }

        .family-number-container {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .family-number-link {
            display: inline-block;
            width: calc(100% - 30px);
            text-decoration: none;
            color: inherit;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .family-number-link:hover {
            background-color: #4db3a8;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(77, 179, 168, 0.3);
        }

        .copy-btn {
            background: #4db3a8;
            color: white;
            border: none;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            cursor: pointer;
            margin-left: 4px;
            opacity: 0;
            transition: all 0.3s ease;
            position: absolute;
            right: 2px;
            top: 50%;
            transform: translateY(-50%);
        }

        .family-number-container:hover .copy-btn {
            opacity: 1;
        }

        .copy-btn:hover {
            background: #3a8f87;
            transform: translateY(-50%) scale(1.1);
        }
    </style>
    <script>
        function copyFamilyNumber(familyNumber, event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            const tempTextArea = document.createElement('textarea');
            tempTextArea.value = familyNumber;
            document.body.appendChild(tempTextArea);
            tempTextArea.select();
            
            try {
                document.execCommand('copy');
                alert('Copied: ' + familyNumber);
            } catch (err) {
                alert('Copy failed');
            }
            
            document.body.removeChild(tempTextArea);
        }
    </script>
</head>
<body>
    <!-- Content is generated above -->
</body>
</html>
