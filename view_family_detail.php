<?php
session_start();
if (!isset($_SESSION['username'])) {
    header("Location: login.php");
    exit();
}

include "db.php";

// Get family ID from URL
if (!isset($_GET['id'])) {
    header("Location: view_family_records.php?error=no_id");
    exit();
}

$family_id = (int)$_GET['id'];

// Validate that we have a valid ID
if ($family_id <= 0) {
    header("Location: view_family_records.php?error=invalid_id");
    exit();
}

// Query to get family record
$sql = "SELECT * FROM family_registration WHERE id = ?";
$stmt = $conn->prepare($sql);

if (!$stmt) {
    error_log("Database prepare error: " . $conn->error);
    header("Location: view_family_records.php?error=database_error");
    exit();
}

$stmt->bind_param("i", $family_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    error_log("No record found for ID: " . $family_id);
    header("Location: view_family_records.php?error=record_not_found&id=" . $family_id);
    exit();
}

$record = $result->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Family Record Details - <?php echo htmlspecialchars($record['family_number']); ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Lexend', sans-serif;
        }

        body {
            background: #f0f7ff;
            min-height: 100vh;
            display: flex;
            padding: 0;
        }

        .dashboard {
            display: flex;
            min-height: 100vh;
            width: 100%;
        }

        .sidebar {
            width: 250px;
            background: #4db3a8;
            color: white;
            padding: 2rem 0;
        }

        .sidebar-header {
            padding: 0 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .sidebar-header img {
            width: 180px;
            height: 180px;
            margin-bottom: 1.5rem;
            border-radius: 50%;
            object-fit: contain;
            padding: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #000;
            line-height: 1.3;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 1rem 2rem;
            color: #000;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            background: #3a8f87;
            color: #000;
        }

        .nav-link i {
            margin-right: 1rem;
            width: 20px;
            color: #000;
        }

        .main-content {
            flex: 1;
            padding: 2rem;
            display: flex;
            justify-content: center;
            align-items: flex-start;
        }

        .detail-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 800px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #4db3a8;
        }

        .header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 16px;
        }

        .family-info {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 25px;
        }

        .info-row {
            display: flex;
            margin-bottom: 20px;
            align-items: flex-start;
        }

        .info-row:last-child {
            margin-bottom: 0;
        }

        .info-label {
            font-weight: 600;
            color: #555;
            width: 180px;
            flex-shrink: 0;
            font-size: 16px;
        }

        .info-value {
            color: #333;
            flex-grow: 1;
            font-size: 16px;
            line-height: 1.5;
        }

        .family-number-value {
            font-weight: 700;
            font-size: 18px;
            color: #4db3a8;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #4db3a8;
            color: white;
        }

        .btn-primary:hover {
            background: #3a8f87;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                padding: 1rem 0;
            }

            .main-content {
                padding: 1rem;
            }

            .info-row {
                flex-direction: column;
                gap: 5px;
            }

            .info-label {
                width: auto;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
    <script>
        // Copy family number function
        function copyFamilyNumber(familyNumber) {
            // Create a temporary textarea element
            const tempTextArea = document.createElement('textarea');
            tempTextArea.value = familyNumber;
            document.body.appendChild(tempTextArea);

            // Select and copy the text
            tempTextArea.select();
            tempTextArea.setSelectionRange(0, 99999); // For mobile devices

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showCopyNotification('Family number copied: ' + familyNumber, 'success');
                } else {
                    // Fallback to modern clipboard API
                    navigator.clipboard.writeText(familyNumber).then(function() {
                        showCopyNotification('Family number copied: ' + familyNumber, 'success');
                    }).catch(function() {
                        showCopyNotification('Failed to copy family number', 'error');
                    });
                }
            } catch (err) {
                // Fallback to modern clipboard API
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(familyNumber).then(function() {
                        showCopyNotification('Family number copied: ' + familyNumber, 'success');
                    }).catch(function() {
                        showCopyNotification('Failed to copy family number', 'error');
                    });
                } else {
                    showCopyNotification('Copy not supported in this browser', 'error');
                }
            }

            // Remove the temporary element
            document.body.removeChild(tempTextArea);
        }

        // Show copy notification
        function showCopyNotification(message, type) {
            // Remove any existing notification
            const existingNotification = document.querySelector('.copy-notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : '#dc3545'};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 8px;
            `;
            notification.className = 'copy-notification';
            notification.innerHTML = `
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
                <span>${message}</span>
            `;

            // Add to page
            document.body.appendChild(notification);

            // Show notification
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Hide notification after 3 seconds
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</head>
<body>
    <div class="dashboard">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="./images/brgy_la_huerta.png" alt="Barangay La Huerta Logo">
                <h2>Family Number Management System</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="dashboard.php" class="nav-link">
                        <i class="fas fa-home"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="family_registration.php" class="nav-link">
                        <i class="fas fa-user-plus"></i>
                        Register Family
                    </a>
                </li>
                <li class="nav-item">
                    <a href="view_family_records.php" class="nav-link active">
                        <i class="fas fa-table"></i>
                        View Records
                    </a>
                </li>
                <li class="nav-item">
                    <a href="scanner.php" class="nav-link">
                        <i class="fas fa-qrcode"></i>
                        Scan Family ID
                    </a>
                </li>
                <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'super admin'): ?>
                <li class="nav-item">
                    <a href="manage_users.php" class="nav-link">
                        <i class="fas fa-users"></i>
                        Manage Users
                    </a>
                </li>
                <?php endif; ?>
                <li class="nav-item">
                    <a href="logout.php" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </li>
            </ul>
        </div>

        <div class="main-content">
            <div class="detail-container">
                <div class="header">
                    <h1>Family Record Details</h1>
                    <p>La Huerta Health Center</p>
                </div>

                <div class="family-info">
                    <div class="info-row">
                        <div class="info-label">Family Number:</div>
                        <div class="info-value family-number-value" style="display: flex; align-items: center; gap: 10px;">
                            <span onclick="copyFamilyNumber('<?php echo htmlspecialchars($record['family_number']); ?>')" style="cursor: pointer; user-select: all;" title="Click to copy"><?php echo htmlspecialchars($record['family_number']); ?></span>
                            <button onclick="copyFamilyNumber('<?php echo htmlspecialchars($record['family_number']); ?>')" style="background: #4db3a8; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;" title="Copy family number">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Registration Date:</div>
                        <div class="info-value"><?php echo date('F d, Y', strtotime($record['registration_date'])); ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Male Name:</div>
                        <div class="info-value"><?php echo htmlspecialchars($record['male_name']); ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Female Name:</div>
                        <div class="info-value"><?php echo htmlspecialchars($record['female_name']); ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Address:</div>
                        <div class="info-value"><?php echo htmlspecialchars($record['address']); ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Remarks:</div>
                        <div class="info-value"><?php echo htmlspecialchars($record['remarks'] ?: 'None'); ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Registered By:</div>
                        <div class="info-value"><?php echo htmlspecialchars(strtoupper($record['registered_by'])); ?></div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Created:</div>
                        <div class="info-value"><?php echo date('F d, Y g:i A', strtotime($record['created_at'])); ?></div>
                    </div>
                </div>

                <div class="action-buttons">
                    <a href="view_family_records.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Records
                    </a>
                    <a href="edit_family.php?id=<?php echo $record['id']; ?>" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Record
                    </a>
                    <a href="print_family.php?id=<?php echo $record['id']; ?>" class="btn btn-success" target="_blank">
                        <i class="fas fa-print"></i> Print ID Card
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
<?php $conn->close(); ?>
